export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      activity_logs: {
        Row: {
          action_type: string
          created_at: string | null
          details: Json | null
          id: string
          organization_id: string
          resource_id: string
          resource_type: string
          user_id: string
        }
        Insert: {
          action_type: string
          created_at?: string | null
          details?: Json | null
          id?: string
          organization_id: string
          resource_id: string
          resource_type: string
          user_id: string
        }
        Update: {
          action_type?: string
          created_at?: string | null
          details?: Json | null
          id?: string
          organization_id?: string
          resource_id?: string
          resource_type?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "activity_logs_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      allergies: {
        Row: {
          allergen: string
          created_at: string | null
          id: string
          metadata: Json | null
          onset_date: string | null
          patient_id: string | null
          reaction: string | null
          reported_by: string | null
          severity: Database["public"]["Enums"]["allergy_severity"]
          status: Database["public"]["Enums"]["allergy_status"] | null
          updated_at: string | null
        }
        Insert: {
          allergen: string
          created_at?: string | null
          id?: string
          metadata?: Json | null
          onset_date?: string | null
          patient_id?: string | null
          reaction?: string | null
          reported_by?: string | null
          severity: Database["public"]["Enums"]["allergy_severity"]
          status?: Database["public"]["Enums"]["allergy_status"] | null
          updated_at?: string | null
        }
        Update: {
          allergen?: string
          created_at?: string | null
          id?: string
          metadata?: Json | null
          onset_date?: string | null
          patient_id?: string | null
          reaction?: string | null
          reported_by?: string | null
          severity?: Database["public"]["Enums"]["allergy_severity"]
          status?: Database["public"]["Enums"]["allergy_status"] | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "allergies_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
        ]
      }
      analytics_events: {
        Row: {
          created_at: string | null
          event_data: Json
          event_type: string
          id: string
          organization_id: string | null
          timestamp: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          event_data: Json
          event_type: string
          id?: string
          organization_id?: string | null
          timestamp?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          event_data?: Json
          event_type?: string
          id?: string
          organization_id?: string | null
          timestamp?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "analytics_events_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      analytics_metrics: {
        Row: {
          created_at: string | null
          dimensions: Json | null
          id: string
          metric_name: string
          metric_value: number
          organization_id: string | null
          timestamp: string | null
        }
        Insert: {
          created_at?: string | null
          dimensions?: Json | null
          id?: string
          metric_name: string
          metric_value: number
          organization_id?: string | null
          timestamp?: string | null
        }
        Update: {
          created_at?: string | null
          dimensions?: Json | null
          id?: string
          metric_name?: string
          metric_value?: number
          organization_id?: string | null
          timestamp?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "analytics_metrics_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      appointments: {
        Row: {
          appointment_date: string
          created_at: string | null
          department_id: string | null
          duration_minutes: number
          id: string
          notes: string | null
          organization_id: string | null
          patient_id: string | null
          provider_id: string | null
          reason: string | null
          status: Database["public"]["Enums"]["appointment_status"]
          updated_at: string | null
        }
        Insert: {
          appointment_date: string
          created_at?: string | null
          department_id?: string | null
          duration_minutes?: number
          id?: string
          notes?: string | null
          organization_id?: string | null
          patient_id?: string | null
          provider_id?: string | null
          reason?: string | null
          status?: Database["public"]["Enums"]["appointment_status"]
          updated_at?: string | null
        }
        Update: {
          appointment_date?: string
          created_at?: string | null
          department_id?: string | null
          duration_minutes?: number
          id?: string
          notes?: string | null
          organization_id?: string | null
          patient_id?: string | null
          provider_id?: string | null
          reason?: string | null
          status?: Database["public"]["Enums"]["appointment_status"]
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "appointments_department_id_fkey"
            columns: ["department_id"]
            isOneToOne: false
            referencedRelation: "departments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "appointments_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "appointments_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "appointments_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "analytics_provider_metrics"
            referencedColumns: ["provider_id"]
          },
          {
            foreignKeyName: "appointments_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "healthcare_providers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "appointments_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "secure_analytics_provider_metrics"
            referencedColumns: ["provider_id"]
          },
        ]
      }
      audit_logs: {
        Row: {
          action: string
          changed_by: string | null
          id: string
          new_data: Json | null
          old_data: Json | null
          record_id: string
          table_name: string
          timestamp: string | null
        }
        Insert: {
          action: string
          changed_by?: string | null
          id?: string
          new_data?: Json | null
          old_data?: Json | null
          record_id: string
          table_name: string
          timestamp?: string | null
        }
        Update: {
          action?: string
          changed_by?: string | null
          id?: string
          new_data?: Json | null
          old_data?: Json | null
          record_id?: string
          table_name?: string
          timestamp?: string | null
        }
        Relationships: []
      }
      billing_codes: {
        Row: {
          code: string
          created_at: string | null
          description: string
          effective_date: string
          end_date: string | null
          id: string
          type: string
          updated_at: string | null
        }
        Insert: {
          code: string
          created_at?: string | null
          description: string
          effective_date: string
          end_date?: string | null
          id?: string
          type: string
          updated_at?: string | null
        }
        Update: {
          code?: string
          created_at?: string | null
          description?: string
          effective_date?: string
          end_date?: string | null
          id?: string
          type?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      care_team_members: {
        Row: {
          created_at: string | null
          end_date: string | null
          id: string
          metadata: Json | null
          notes: string | null
          patient_id: string | null
          primary_contact: boolean | null
          provider_id: string | null
          role: string
          start_date: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          end_date?: string | null
          id?: string
          metadata?: Json | null
          notes?: string | null
          patient_id?: string | null
          primary_contact?: boolean | null
          provider_id?: string | null
          role: string
          start_date: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          end_date?: string | null
          id?: string
          metadata?: Json | null
          notes?: string | null
          patient_id?: string | null
          primary_contact?: boolean | null
          provider_id?: string | null
          role?: string
          start_date?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "care_team_members_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "care_team_members_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "analytics_provider_metrics"
            referencedColumns: ["provider_id"]
          },
          {
            foreignKeyName: "care_team_members_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "healthcare_providers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "care_team_members_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "secure_analytics_provider_metrics"
            referencedColumns: ["provider_id"]
          },
        ]
      }
      claims: {
        Row: {
          amount: number
          billing_codes: Json
          created_at: string | null
          id: string
          insurance_provider_id: string | null
          metadata: Json | null
          patient_id: string | null
          processed_at: string | null
          provider_id: string | null
          service_date: string
          status: string
          submitted_at: string | null
          updated_at: string | null
        }
        Insert: {
          amount: number
          billing_codes: Json
          created_at?: string | null
          id?: string
          insurance_provider_id?: string | null
          metadata?: Json | null
          patient_id?: string | null
          processed_at?: string | null
          provider_id?: string | null
          service_date: string
          status: string
          submitted_at?: string | null
          updated_at?: string | null
        }
        Update: {
          amount?: number
          billing_codes?: Json
          created_at?: string | null
          id?: string
          insurance_provider_id?: string | null
          metadata?: Json | null
          patient_id?: string | null
          processed_at?: string | null
          provider_id?: string | null
          service_date?: string
          status?: string
          submitted_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "claims_insurance_provider_id_fkey"
            columns: ["insurance_provider_id"]
            isOneToOne: false
            referencedRelation: "insurance_providers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "claims_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "claims_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "analytics_provider_metrics"
            referencedColumns: ["provider_id"]
          },
          {
            foreignKeyName: "claims_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "healthcare_providers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "claims_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "secure_analytics_provider_metrics"
            referencedColumns: ["provider_id"]
          },
        ]
      }
      clinical_notes: {
        Row: {
          content: string
          created_at: string | null
          id: string
          medical_record_id: string | null
          metadata: Json | null
          note_type: string
          signed_at: string | null
          signed_by: string | null
          updated_at: string | null
        }
        Insert: {
          content: string
          created_at?: string | null
          id?: string
          medical_record_id?: string | null
          metadata?: Json | null
          note_type: string
          signed_at?: string | null
          signed_by?: string | null
          updated_at?: string | null
        }
        Update: {
          content?: string
          created_at?: string | null
          id?: string
          medical_record_id?: string | null
          metadata?: Json | null
          note_type?: string
          signed_at?: string | null
          signed_by?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "clinical_notes_medical_record_id_fkey"
            columns: ["medical_record_id"]
            isOneToOne: false
            referencedRelation: "medical_records"
            referencedColumns: ["id"]
          },
        ]
      }
      conversation_participants: {
        Row: {
          conversation_id: string | null
          created_at: string | null
          id: string
          last_read_at: string | null
          role: string | null
          user_id: string | null
        }
        Insert: {
          conversation_id?: string | null
          created_at?: string | null
          id?: string
          last_read_at?: string | null
          role?: string | null
          user_id?: string | null
        }
        Update: {
          conversation_id?: string | null
          created_at?: string | null
          id?: string
          last_read_at?: string | null
          role?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "conversation_participants_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "conversations"
            referencedColumns: ["id"]
          },
        ]
      }
      conversations: {
        Row: {
          created_at: string | null
          created_by: string | null
          id: string
          metadata: Json | null
          organization_id: string | null
          title: string | null
          type: Database["public"]["Enums"]["conversation_type"]
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          id?: string
          metadata?: Json | null
          organization_id?: string | null
          title?: string | null
          type: Database["public"]["Enums"]["conversation_type"]
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          id?: string
          metadata?: Json | null
          organization_id?: string | null
          title?: string | null
          type?: Database["public"]["Enums"]["conversation_type"]
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "conversations_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      departments: {
        Row: {
          created_at: string | null
          facility_id: string | null
          id: string
          name: string
          settings: Json | null
          type: Database["public"]["Enums"]["department_type"]
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          facility_id?: string | null
          id?: string
          name: string
          settings?: Json | null
          type: Database["public"]["Enums"]["department_type"]
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          facility_id?: string | null
          id?: string
          name?: string
          settings?: Json | null
          type?: Database["public"]["Enums"]["department_type"]
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "departments_facility_id_fkey"
            columns: ["facility_id"]
            isOneToOne: false
            referencedRelation: "facilities"
            referencedColumns: ["id"]
          },
        ]
      }
      documents: {
        Row: {
          created_at: string | null
          created_by: string | null
          description: string | null
          document_type: string
          file_path: string
          id: string
          metadata: Json | null
          mime_type: string
          organization_id: string | null
          patient_id: string | null
          title: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          document_type: string
          file_path: string
          id?: string
          metadata?: Json | null
          mime_type: string
          organization_id?: string | null
          patient_id?: string | null
          title: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          document_type?: string
          file_path?: string
          id?: string
          metadata?: Json | null
          mime_type?: string
          organization_id?: string | null
          patient_id?: string | null
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "documents_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "documents_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
        ]
      }
      education_materials: {
        Row: {
          category: string
          content: string
          created_at: string | null
          format: Database["public"]["Enums"]["material_format"]
          id: string
          language: string | null
          metadata: Json | null
          title: string
          updated_at: string | null
        }
        Insert: {
          category: string
          content: string
          created_at?: string | null
          format: Database["public"]["Enums"]["material_format"]
          id?: string
          language?: string | null
          metadata?: Json | null
          title: string
          updated_at?: string | null
        }
        Update: {
          category?: string
          content?: string
          created_at?: string | null
          format?: Database["public"]["Enums"]["material_format"]
          id?: string
          language?: string | null
          metadata?: Json | null
          title?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      facilities: {
        Row: {
          address: Json
          contact_info: Json
          created_at: string | null
          id: string
          name: string
          operating_hours: Json | null
          organization_id: string | null
          settings: Json | null
          type: string
          updated_at: string | null
        }
        Insert: {
          address: Json
          contact_info: Json
          created_at?: string | null
          id?: string
          name: string
          operating_hours?: Json | null
          organization_id?: string | null
          settings?: Json | null
          type: string
          updated_at?: string | null
        }
        Update: {
          address?: Json
          contact_info?: Json
          created_at?: string | null
          id?: string
          name?: string
          operating_hours?: Json | null
          organization_id?: string | null
          settings?: Json | null
          type?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "facilities_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      healthcare_providers: {
        Row: {
          created_at: string | null
          credentials: Json | null
          department_id: string | null
          first_name: string
          id: string
          last_name: string
          license_number: string | null
          organization_id: string | null
          permissions: Json | null
          provider_type: Database["public"]["Enums"]["provider_type"]
          role: Database["public"]["Enums"]["user_role"] | null
          schedule_settings: Json | null
          specialization: string | null
          specialties: string[] | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          credentials?: Json | null
          department_id?: string | null
          first_name: string
          id?: string
          last_name: string
          license_number?: string | null
          organization_id?: string | null
          permissions?: Json | null
          provider_type: Database["public"]["Enums"]["provider_type"]
          role?: Database["public"]["Enums"]["user_role"] | null
          schedule_settings?: Json | null
          specialization?: string | null
          specialties?: string[] | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          credentials?: Json | null
          department_id?: string | null
          first_name?: string
          id?: string
          last_name?: string
          license_number?: string | null
          organization_id?: string | null
          permissions?: Json | null
          provider_type?: Database["public"]["Enums"]["provider_type"]
          role?: Database["public"]["Enums"]["user_role"] | null
          schedule_settings?: Json | null
          specialization?: string | null
          specialties?: string[] | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "healthcare_providers_department_id_fkey"
            columns: ["department_id"]
            isOneToOne: false
            referencedRelation: "departments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "healthcare_providers_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      immunizations: {
        Row: {
          administered_by: string | null
          administered_date: string
          created_at: string | null
          dose_number: number | null
          expiration_date: string | null
          id: string
          lot_number: string | null
          manufacturer: string | null
          metadata: Json | null
          notes: string | null
          patient_id: string | null
          route: Database["public"]["Enums"]["administration_route"] | null
          site: Database["public"]["Enums"]["administration_site"] | null
          updated_at: string | null
          vaccine_code: string | null
          vaccine_name: string
        }
        Insert: {
          administered_by?: string | null
          administered_date: string
          created_at?: string | null
          dose_number?: number | null
          expiration_date?: string | null
          id?: string
          lot_number?: string | null
          manufacturer?: string | null
          metadata?: Json | null
          notes?: string | null
          patient_id?: string | null
          route?: Database["public"]["Enums"]["administration_route"] | null
          site?: Database["public"]["Enums"]["administration_site"] | null
          updated_at?: string | null
          vaccine_code?: string | null
          vaccine_name: string
        }
        Update: {
          administered_by?: string | null
          administered_date?: string
          created_at?: string | null
          dose_number?: number | null
          expiration_date?: string | null
          id?: string
          lot_number?: string | null
          manufacturer?: string | null
          metadata?: Json | null
          notes?: string | null
          patient_id?: string | null
          route?: Database["public"]["Enums"]["administration_route"] | null
          site?: Database["public"]["Enums"]["administration_site"] | null
          updated_at?: string | null
          vaccine_code?: string | null
          vaccine_name?: string
        }
        Relationships: [
          {
            foreignKeyName: "immunizations_administered_by_fkey"
            columns: ["administered_by"]
            isOneToOne: false
            referencedRelation: "analytics_provider_metrics"
            referencedColumns: ["provider_id"]
          },
          {
            foreignKeyName: "immunizations_administered_by_fkey"
            columns: ["administered_by"]
            isOneToOne: false
            referencedRelation: "healthcare_providers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "immunizations_administered_by_fkey"
            columns: ["administered_by"]
            isOneToOne: false
            referencedRelation: "secure_analytics_provider_metrics"
            referencedColumns: ["provider_id"]
          },
          {
            foreignKeyName: "immunizations_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
        ]
      }
      insurance_providers: {
        Row: {
          contact_info: Json
          created_at: string | null
          id: string
          name: string
          settings: Json | null
          updated_at: string | null
        }
        Insert: {
          contact_info: Json
          created_at?: string | null
          id?: string
          name: string
          settings?: Json | null
          updated_at?: string | null
        }
        Update: {
          contact_info?: Json
          created_at?: string | null
          id?: string
          name?: string
          settings?: Json | null
          updated_at?: string | null
        }
        Relationships: []
      }
      inventory_items: {
        Row: {
          created_at: string | null
          facility_id: string | null
          id: string
          location: string | null
          metadata: Json | null
          minimum_quantity: number | null
          name: string
          organization_id: string | null
          quantity: number
          type: string
          unit: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          facility_id?: string | null
          id?: string
          location?: string | null
          metadata?: Json | null
          minimum_quantity?: number | null
          name: string
          organization_id?: string | null
          quantity: number
          type: string
          unit: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          facility_id?: string | null
          id?: string
          location?: string | null
          metadata?: Json | null
          minimum_quantity?: number | null
          name?: string
          organization_id?: string | null
          quantity?: number
          type?: string
          unit?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "inventory_items_facility_id_fkey"
            columns: ["facility_id"]
            isOneToOne: false
            referencedRelation: "facilities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "inventory_items_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      inventory_transactions: {
        Row: {
          created_at: string | null
          id: string
          item_id: string | null
          metadata: Json | null
          performed_by: string | null
          quantity: number
          reason: string | null
          transaction_type: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          item_id?: string | null
          metadata?: Json | null
          performed_by?: string | null
          quantity: number
          reason?: string | null
          transaction_type: string
        }
        Update: {
          created_at?: string | null
          id?: string
          item_id?: string | null
          metadata?: Json | null
          performed_by?: string | null
          quantity?: number
          reason?: string | null
          transaction_type?: string
        }
        Relationships: [
          {
            foreignKeyName: "inventory_transactions_item_id_fkey"
            columns: ["item_id"]
            isOneToOne: false
            referencedRelation: "inventory_items"
            referencedColumns: ["id"]
          },
        ]
      }
      lab_results: {
        Row: {
          created_at: string | null
          id: string
          normal_range: Json | null
          notes: string | null
          patient_id: string | null
          provider_id: string | null
          results: Json
          test_date: string
          test_name: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          normal_range?: Json | null
          notes?: string | null
          patient_id?: string | null
          provider_id?: string | null
          results: Json
          test_date: string
          test_name: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          normal_range?: Json | null
          notes?: string | null
          patient_id?: string | null
          provider_id?: string | null
          results?: Json
          test_date?: string
          test_name?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "lab_results_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "lab_results_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "analytics_provider_metrics"
            referencedColumns: ["provider_id"]
          },
          {
            foreignKeyName: "lab_results_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "healthcare_providers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "lab_results_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "secure_analytics_provider_metrics"
            referencedColumns: ["provider_id"]
          },
        ]
      }
      medical_records: {
        Row: {
          attachments: Json | null
          chief_complaint: string | null
          created_at: string | null
          department_id: string | null
          diagnosis: string[] | null
          id: string
          notes: string | null
          organization_id: string | null
          patient_id: string | null
          provider_id: string | null
          search_vector: unknown | null
          treatment_plan: string | null
          updated_at: string | null
          visit_date: string
        }
        Insert: {
          attachments?: Json | null
          chief_complaint?: string | null
          created_at?: string | null
          department_id?: string | null
          diagnosis?: string[] | null
          id?: string
          notes?: string | null
          organization_id?: string | null
          patient_id?: string | null
          provider_id?: string | null
          search_vector?: unknown | null
          treatment_plan?: string | null
          updated_at?: string | null
          visit_date: string
        }
        Update: {
          attachments?: Json | null
          chief_complaint?: string | null
          created_at?: string | null
          department_id?: string | null
          diagnosis?: string[] | null
          id?: string
          notes?: string | null
          organization_id?: string | null
          patient_id?: string | null
          provider_id?: string | null
          search_vector?: unknown | null
          treatment_plan?: string | null
          updated_at?: string | null
          visit_date?: string
        }
        Relationships: [
          {
            foreignKeyName: "medical_records_department_id_fkey"
            columns: ["department_id"]
            isOneToOne: false
            referencedRelation: "departments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "medical_records_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "medical_records_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "medical_records_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "analytics_provider_metrics"
            referencedColumns: ["provider_id"]
          },
          {
            foreignKeyName: "medical_records_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "healthcare_providers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "medical_records_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "secure_analytics_provider_metrics"
            referencedColumns: ["provider_id"]
          },
        ]
      }
      medications: {
        Row: {
          active: boolean | null
          created_at: string | null
          dosage: string
          end_date: string | null
          frequency: string
          id: string
          instructions: string | null
          medication_name: string
          patient_id: string | null
          provider_id: string | null
          start_date: string
          updated_at: string | null
        }
        Insert: {
          active?: boolean | null
          created_at?: string | null
          dosage: string
          end_date?: string | null
          frequency: string
          id?: string
          instructions?: string | null
          medication_name: string
          patient_id?: string | null
          provider_id?: string | null
          start_date: string
          updated_at?: string | null
        }
        Update: {
          active?: boolean | null
          created_at?: string | null
          dosage?: string
          end_date?: string | null
          frequency?: string
          id?: string
          instructions?: string | null
          medication_name?: string
          patient_id?: string | null
          provider_id?: string | null
          start_date?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "medications_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "medications_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "analytics_provider_metrics"
            referencedColumns: ["provider_id"]
          },
          {
            foreignKeyName: "medications_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "healthcare_providers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "medications_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "secure_analytics_provider_metrics"
            referencedColumns: ["provider_id"]
          },
        ]
      }
      message_states: {
        Row: {
          id: string
          message_id: string | null
          state: Database["public"]["Enums"]["message_state"]
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          id?: string
          message_id?: string | null
          state: Database["public"]["Enums"]["message_state"]
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          id?: string
          message_id?: string | null
          state?: Database["public"]["Enums"]["message_state"]
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "message_states_message_id_fkey"
            columns: ["message_id"]
            isOneToOne: false
            referencedRelation: "messages"
            referencedColumns: ["id"]
          },
        ]
      }
      messages: {
        Row: {
          attachments: Json | null
          content: string
          conversation_id: string | null
          created_at: string | null
          id: string
          metadata: Json | null
          sender_id: string | null
          updated_at: string | null
        }
        Insert: {
          attachments?: Json | null
          content: string
          conversation_id?: string | null
          created_at?: string | null
          id?: string
          metadata?: Json | null
          sender_id?: string | null
          updated_at?: string | null
        }
        Update: {
          attachments?: Json | null
          content?: string
          conversation_id?: string | null
          created_at?: string | null
          id?: string
          metadata?: Json | null
          sender_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "messages_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "conversations"
            referencedColumns: ["id"]
          },
        ]
      }
      notification_preferences: {
        Row: {
          created_at: string | null
          email_enabled: boolean | null
          id: string
          in_app_enabled: boolean | null
          push_enabled: boolean | null
          sms_enabled: boolean | null
          type: Database["public"]["Enums"]["notification_type"]
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          email_enabled?: boolean | null
          id?: string
          in_app_enabled?: boolean | null
          push_enabled?: boolean | null
          sms_enabled?: boolean | null
          type: Database["public"]["Enums"]["notification_type"]
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          email_enabled?: boolean | null
          id?: string
          in_app_enabled?: boolean | null
          push_enabled?: boolean | null
          sms_enabled?: boolean | null
          type?: Database["public"]["Enums"]["notification_type"]
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      notification_templates: {
        Row: {
          content_template: string
          created_at: string | null
          id: string
          metadata_template: Json | null
          name: string
          organization_id: string | null
          subject_template: string
          type: Database["public"]["Enums"]["notification_type"]
          updated_at: string | null
        }
        Insert: {
          content_template: string
          created_at?: string | null
          id?: string
          metadata_template?: Json | null
          name: string
          organization_id?: string | null
          subject_template: string
          type: Database["public"]["Enums"]["notification_type"]
          updated_at?: string | null
        }
        Update: {
          content_template?: string
          created_at?: string | null
          id?: string
          metadata_template?: Json | null
          name?: string
          organization_id?: string | null
          subject_template?: string
          type?: Database["public"]["Enums"]["notification_type"]
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "notification_templates_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      notifications: {
        Row: {
          content: string
          created_at: string | null
          id: string
          metadata: Json | null
          organization_id: string | null
          priority: Database["public"]["Enums"]["notification_priority"]
          read_at: string | null
          recipient_id: string | null
          scheduled_for: string | null
          sender_id: string | null
          sent_at: string | null
          status: Database["public"]["Enums"]["notification_status"]
          title: string
          type: Database["public"]["Enums"]["notification_type"]
          updated_at: string | null
        }
        Insert: {
          content: string
          created_at?: string | null
          id?: string
          metadata?: Json | null
          organization_id?: string | null
          priority?: Database["public"]["Enums"]["notification_priority"]
          read_at?: string | null
          recipient_id?: string | null
          scheduled_for?: string | null
          sender_id?: string | null
          sent_at?: string | null
          status?: Database["public"]["Enums"]["notification_status"]
          title: string
          type: Database["public"]["Enums"]["notification_type"]
          updated_at?: string | null
        }
        Update: {
          content?: string
          created_at?: string | null
          id?: string
          metadata?: Json | null
          organization_id?: string | null
          priority?: Database["public"]["Enums"]["notification_priority"]
          read_at?: string | null
          recipient_id?: string | null
          scheduled_for?: string | null
          sender_id?: string | null
          sent_at?: string | null
          status?: Database["public"]["Enums"]["notification_status"]
          title?: string
          type?: Database["public"]["Enums"]["notification_type"]
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "notifications_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      orders: {
        Row: {
          completed_at: string | null
          created_at: string | null
          diagnosis_codes: Json | null
          id: string
          metadata: Json | null
          notes: string | null
          order_details: Json
          order_type: Database["public"]["Enums"]["order_type"]
          ordered_at: string
          ordering_provider_id: string | null
          patient_id: string | null
          priority: Database["public"]["Enums"]["order_priority"] | null
          scheduled_date: string | null
          status: Database["public"]["Enums"]["order_status"] | null
          updated_at: string | null
        }
        Insert: {
          completed_at?: string | null
          created_at?: string | null
          diagnosis_codes?: Json | null
          id?: string
          metadata?: Json | null
          notes?: string | null
          order_details: Json
          order_type: Database["public"]["Enums"]["order_type"]
          ordered_at?: string
          ordering_provider_id?: string | null
          patient_id?: string | null
          priority?: Database["public"]["Enums"]["order_priority"] | null
          scheduled_date?: string | null
          status?: Database["public"]["Enums"]["order_status"] | null
          updated_at?: string | null
        }
        Update: {
          completed_at?: string | null
          created_at?: string | null
          diagnosis_codes?: Json | null
          id?: string
          metadata?: Json | null
          notes?: string | null
          order_details?: Json
          order_type?: Database["public"]["Enums"]["order_type"]
          ordered_at?: string
          ordering_provider_id?: string | null
          patient_id?: string | null
          priority?: Database["public"]["Enums"]["order_priority"] | null
          scheduled_date?: string | null
          status?: Database["public"]["Enums"]["order_status"] | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "orders_ordering_provider_id_fkey"
            columns: ["ordering_provider_id"]
            isOneToOne: false
            referencedRelation: "analytics_provider_metrics"
            referencedColumns: ["provider_id"]
          },
          {
            foreignKeyName: "orders_ordering_provider_id_fkey"
            columns: ["ordering_provider_id"]
            isOneToOne: false
            referencedRelation: "healthcare_providers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "orders_ordering_provider_id_fkey"
            columns: ["ordering_provider_id"]
            isOneToOne: false
            referencedRelation: "secure_analytics_provider_metrics"
            referencedColumns: ["provider_id"]
          },
          {
            foreignKeyName: "orders_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
        ]
      }
      organization_invites: {
        Row: {
          created_at: string | null
          email: string
          expires_at: string | null
          id: string
          invited_by: string | null
          organization_id: string | null
          role: Database["public"]["Enums"]["user_role"]
          status: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          email: string
          expires_at?: string | null
          id?: string
          invited_by?: string | null
          organization_id?: string | null
          role: Database["public"]["Enums"]["user_role"]
          status?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          email?: string
          expires_at?: string | null
          id?: string
          invited_by?: string | null
          organization_id?: string | null
          role?: Database["public"]["Enums"]["user_role"]
          status?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "organization_invites_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      organizations: {
        Row: {
          billing_info: Json | null
          created_at: string | null
          id: string
          name: string
          owner_id: string | null
          settings: Json | null
          subscription_tier: string | null
          type: string
          updated_at: string | null
        }
        Insert: {
          billing_info?: Json | null
          created_at?: string | null
          id?: string
          name: string
          owner_id?: string | null
          settings?: Json | null
          subscription_tier?: string | null
          type: string
          updated_at?: string | null
        }
        Update: {
          billing_info?: Json | null
          created_at?: string | null
          id?: string
          name?: string
          owner_id?: string | null
          settings?: Json | null
          subscription_tier?: string | null
          type?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      patient_alerts: {
        Row: {
          alert_type: string
          created_at: string | null
          created_by: string | null
          description: string
          end_date: string | null
          id: string
          metadata: Json | null
          patient_id: string | null
          severity: Database["public"]["Enums"]["alert_severity"]
          start_date: string
          status: Database["public"]["Enums"]["alert_status"] | null
          updated_at: string | null
        }
        Insert: {
          alert_type: string
          created_at?: string | null
          created_by?: string | null
          description: string
          end_date?: string | null
          id?: string
          metadata?: Json | null
          patient_id?: string | null
          severity: Database["public"]["Enums"]["alert_severity"]
          start_date: string
          status?: Database["public"]["Enums"]["alert_status"] | null
          updated_at?: string | null
        }
        Update: {
          alert_type?: string
          created_at?: string | null
          created_by?: string | null
          description?: string
          end_date?: string | null
          id?: string
          metadata?: Json | null
          patient_id?: string | null
          severity?: Database["public"]["Enums"]["alert_severity"]
          start_date?: string
          status?: Database["public"]["Enums"]["alert_status"] | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "patient_alerts_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
        ]
      }
      patient_education_records: {
        Row: {
          created_at: string | null
          id: string
          material_id: string | null
          metadata: Json | null
          notes: string | null
          patient_id: string | null
          provided_date: string
          provider_id: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          material_id?: string | null
          metadata?: Json | null
          notes?: string | null
          patient_id?: string | null
          provided_date?: string
          provider_id?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          material_id?: string | null
          metadata?: Json | null
          notes?: string | null
          patient_id?: string | null
          provided_date?: string
          provider_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "patient_education_records_material_id_fkey"
            columns: ["material_id"]
            isOneToOne: false
            referencedRelation: "education_materials"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "patient_education_records_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "patient_education_records_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "analytics_provider_metrics"
            referencedColumns: ["provider_id"]
          },
          {
            foreignKeyName: "patient_education_records_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "healthcare_providers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "patient_education_records_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "secure_analytics_provider_metrics"
            referencedColumns: ["provider_id"]
          },
        ]
      }
      patient_portal_settings: {
        Row: {
          communication_settings: Json | null
          created_at: string | null
          id: string
          patient_id: string | null
          preferences: Json | null
          updated_at: string | null
        }
        Insert: {
          communication_settings?: Json | null
          created_at?: string | null
          id?: string
          patient_id?: string | null
          preferences?: Json | null
          updated_at?: string | null
        }
        Update: {
          communication_settings?: Json | null
          created_at?: string | null
          id?: string
          patient_id?: string | null
          preferences?: Json | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "patient_portal_settings_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
        ]
      }
      patient_questionnaires: {
        Row: {
          completed_at: string | null
          created_at: string | null
          id: string
          patient_id: string | null
          questionnaire_type: string
          responses: Json
          updated_at: string | null
        }
        Insert: {
          completed_at?: string | null
          created_at?: string | null
          id?: string
          patient_id?: string | null
          questionnaire_type: string
          responses: Json
          updated_at?: string | null
        }
        Update: {
          completed_at?: string | null
          created_at?: string | null
          id?: string
          patient_id?: string | null
          questionnaire_type?: string
          responses?: Json
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "patient_questionnaires_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
        ]
      }
      patients: {
        Row: {
          address: string | null
          created_at: string | null
          date_of_birth: string
          email: string | null
          emergency_contact: string | null
          first_name: string
          gender: Database["public"]["Enums"]["gender"]
          id: string
          insurance_info: Json | null
          last_name: string
          medical_history: Json | null
          organization_id: string | null
          phone: string | null
          search_vector: unknown | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          address?: string | null
          created_at?: string | null
          date_of_birth: string
          email?: string | null
          emergency_contact?: string | null
          first_name: string
          gender: Database["public"]["Enums"]["gender"]
          id?: string
          insurance_info?: Json | null
          last_name: string
          medical_history?: Json | null
          organization_id?: string | null
          phone?: string | null
          search_vector?: unknown | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          address?: string | null
          created_at?: string | null
          date_of_birth?: string
          email?: string | null
          emergency_contact?: string | null
          first_name?: string
          gender?: Database["public"]["Enums"]["gender"]
          id?: string
          insurance_info?: Json | null
          last_name?: string
          medical_history?: Json | null
          organization_id?: string | null
          phone?: string | null
          search_vector?: unknown | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "patients_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      referrals: {
        Row: {
          completed_date: string | null
          created_at: string | null
          id: string
          metadata: Json | null
          notes: string | null
          patient_id: string | null
          priority: Database["public"]["Enums"]["referral_priority"] | null
          reason: string
          referral_date: string
          referred_to_provider_id: string | null
          referring_provider_id: string | null
          scheduled_date: string | null
          status: Database["public"]["Enums"]["referral_status"] | null
          updated_at: string | null
        }
        Insert: {
          completed_date?: string | null
          created_at?: string | null
          id?: string
          metadata?: Json | null
          notes?: string | null
          patient_id?: string | null
          priority?: Database["public"]["Enums"]["referral_priority"] | null
          reason: string
          referral_date: string
          referred_to_provider_id?: string | null
          referring_provider_id?: string | null
          scheduled_date?: string | null
          status?: Database["public"]["Enums"]["referral_status"] | null
          updated_at?: string | null
        }
        Update: {
          completed_date?: string | null
          created_at?: string | null
          id?: string
          metadata?: Json | null
          notes?: string | null
          patient_id?: string | null
          priority?: Database["public"]["Enums"]["referral_priority"] | null
          reason?: string
          referral_date?: string
          referred_to_provider_id?: string | null
          referring_provider_id?: string | null
          scheduled_date?: string | null
          status?: Database["public"]["Enums"]["referral_status"] | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "referrals_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "referrals_referred_to_provider_id_fkey"
            columns: ["referred_to_provider_id"]
            isOneToOne: false
            referencedRelation: "analytics_provider_metrics"
            referencedColumns: ["provider_id"]
          },
          {
            foreignKeyName: "referrals_referred_to_provider_id_fkey"
            columns: ["referred_to_provider_id"]
            isOneToOne: false
            referencedRelation: "healthcare_providers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "referrals_referred_to_provider_id_fkey"
            columns: ["referred_to_provider_id"]
            isOneToOne: false
            referencedRelation: "secure_analytics_provider_metrics"
            referencedColumns: ["provider_id"]
          },
          {
            foreignKeyName: "referrals_referring_provider_id_fkey"
            columns: ["referring_provider_id"]
            isOneToOne: false
            referencedRelation: "analytics_provider_metrics"
            referencedColumns: ["provider_id"]
          },
          {
            foreignKeyName: "referrals_referring_provider_id_fkey"
            columns: ["referring_provider_id"]
            isOneToOne: false
            referencedRelation: "healthcare_providers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "referrals_referring_provider_id_fkey"
            columns: ["referring_provider_id"]
            isOneToOne: false
            referencedRelation: "secure_analytics_provider_metrics"
            referencedColumns: ["provider_id"]
          },
        ]
      }
      role_definitions: {
        Row: {
          base_permissions: Json
          created_at: string
          description: string
          role: Database["public"]["Enums"]["user_role"]
          updated_at: string
        }
        Insert: {
          base_permissions: Json
          created_at?: string
          description: string
          role: Database["public"]["Enums"]["user_role"]
          updated_at?: string
        }
        Update: {
          base_permissions?: Json
          created_at?: string
          description?: string
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string
        }
        Relationships: []
      }
      role_permissions: {
        Row: {
          actions: string[]
          conditions: Json | null
          created_at: string | null
          id: string
          resource: string
          role: Database["public"]["Enums"]["user_role"]
          updated_at: string | null
        }
        Insert: {
          actions: string[]
          conditions?: Json | null
          created_at?: string | null
          id?: string
          resource: string
          role: Database["public"]["Enums"]["user_role"]
          updated_at?: string | null
        }
        Update: {
          actions?: string[]
          conditions?: Json | null
          created_at?: string | null
          id?: string
          resource?: string
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_role_permissions_role"
            columns: ["role"]
            isOneToOne: false
            referencedRelation: "role_definitions"
            referencedColumns: ["role"]
          },
        ]
      }
      task_comments: {
        Row: {
          attachments: Json | null
          content: string
          created_at: string | null
          id: string
          task_id: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          attachments?: Json | null
          content: string
          created_at?: string | null
          id?: string
          task_id?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          attachments?: Json | null
          content?: string
          created_at?: string | null
          id?: string
          task_id?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "task_comments_task_id_fkey"
            columns: ["task_id"]
            isOneToOne: false
            referencedRelation: "tasks"
            referencedColumns: ["id"]
          },
        ]
      }
      task_watchers: {
        Row: {
          created_at: string | null
          id: string
          task_id: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          task_id?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          task_id?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "task_watchers_task_id_fkey"
            columns: ["task_id"]
            isOneToOne: false
            referencedRelation: "tasks"
            referencedColumns: ["id"]
          },
        ]
      }
      tasks: {
        Row: {
          assigned_by: string | null
          assigned_to: string | null
          completed_at: string | null
          created_at: string | null
          department_id: string | null
          description: string | null
          due_date: string | null
          id: string
          metadata: Json | null
          organization_id: string | null
          priority: Database["public"]["Enums"]["task_priority"]
          related_to: Json | null
          status: Database["public"]["Enums"]["task_status"]
          title: string
          updated_at: string | null
        }
        Insert: {
          assigned_by?: string | null
          assigned_to?: string | null
          completed_at?: string | null
          created_at?: string | null
          department_id?: string | null
          description?: string | null
          due_date?: string | null
          id?: string
          metadata?: Json | null
          organization_id?: string | null
          priority?: Database["public"]["Enums"]["task_priority"]
          related_to?: Json | null
          status?: Database["public"]["Enums"]["task_status"]
          title: string
          updated_at?: string | null
        }
        Update: {
          assigned_by?: string | null
          assigned_to?: string | null
          completed_at?: string | null
          created_at?: string | null
          department_id?: string | null
          description?: string | null
          due_date?: string | null
          id?: string
          metadata?: Json | null
          organization_id?: string | null
          priority?: Database["public"]["Enums"]["task_priority"]
          related_to?: Json | null
          status?: Database["public"]["Enums"]["task_status"]
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tasks_department_id_fkey"
            columns: ["department_id"]
            isOneToOne: false
            referencedRelation: "departments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tasks_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      teams: {
        Row: {
          created_at: string | null
          department_id: string | null
          description: string | null
          id: string
          leader_id: string | null
          members: Json | null
          name: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          department_id?: string | null
          description?: string | null
          id?: string
          leader_id?: string | null
          members?: Json | null
          name: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          department_id?: string | null
          description?: string | null
          id?: string
          leader_id?: string | null
          members?: Json | null
          name?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "teams_department_id_fkey"
            columns: ["department_id"]
            isOneToOne: false
            referencedRelation: "departments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "teams_leader_id_fkey"
            columns: ["leader_id"]
            isOneToOne: false
            referencedRelation: "analytics_provider_metrics"
            referencedColumns: ["provider_id"]
          },
          {
            foreignKeyName: "teams_leader_id_fkey"
            columns: ["leader_id"]
            isOneToOne: false
            referencedRelation: "healthcare_providers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "teams_leader_id_fkey"
            columns: ["leader_id"]
            isOneToOne: false
            referencedRelation: "secure_analytics_provider_metrics"
            referencedColumns: ["provider_id"]
          },
        ]
      }
      templates: {
        Row: {
          content: string
          created_at: string | null
          id: string
          metadata: Json | null
          name: string
          organization_id: string | null
          type: string
          updated_at: string | null
        }
        Insert: {
          content: string
          created_at?: string | null
          id?: string
          metadata?: Json | null
          name: string
          organization_id?: string | null
          type: string
          updated_at?: string | null
        }
        Update: {
          content?: string
          created_at?: string | null
          id?: string
          metadata?: Json | null
          name?: string
          organization_id?: string | null
          type?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "templates_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      user_roles: {
        Row: {
          created_at: string | null
          custom_permissions: Json | null
          department_id: string | null
          id: string
          invitation_status: string | null
          organization_id: string | null
          role: Database["public"]["Enums"]["user_role"]
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          custom_permissions?: Json | null
          department_id?: string | null
          id?: string
          invitation_status?: string | null
          organization_id?: string | null
          role: Database["public"]["Enums"]["user_role"]
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          custom_permissions?: Json | null
          department_id?: string | null
          id?: string
          invitation_status?: string | null
          organization_id?: string | null
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_user_roles_role"
            columns: ["role"]
            isOneToOne: false
            referencedRelation: "role_definitions"
            referencedColumns: ["role"]
          },
          {
            foreignKeyName: "user_roles_department_id_fkey"
            columns: ["department_id"]
            isOneToOne: false
            referencedRelation: "departments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_roles_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      vital_signs: {
        Row: {
          blood_pressure_diastolic: number | null
          blood_pressure_systolic: number | null
          bmi: number | null
          created_at: string | null
          heart_rate: number | null
          height: number | null
          id: string
          metadata: Json | null
          notes: string | null
          oxygen_saturation: number | null
          pain_level: number | null
          patient_id: string | null
          recorded_at: string
          recorded_by: string | null
          respiratory_rate: number | null
          temperature: number | null
          temperature_unit: string | null
          updated_at: string | null
          weight: number | null
        }
        Insert: {
          blood_pressure_diastolic?: number | null
          blood_pressure_systolic?: number | null
          bmi?: number | null
          created_at?: string | null
          heart_rate?: number | null
          height?: number | null
          id?: string
          metadata?: Json | null
          notes?: string | null
          oxygen_saturation?: number | null
          pain_level?: number | null
          patient_id?: string | null
          recorded_at?: string
          recorded_by?: string | null
          respiratory_rate?: number | null
          temperature?: number | null
          temperature_unit?: string | null
          updated_at?: string | null
          weight?: number | null
        }
        Update: {
          blood_pressure_diastolic?: number | null
          blood_pressure_systolic?: number | null
          bmi?: number | null
          created_at?: string | null
          heart_rate?: number | null
          height?: number | null
          id?: string
          metadata?: Json | null
          notes?: string | null
          oxygen_saturation?: number | null
          pain_level?: number | null
          patient_id?: string | null
          recorded_at?: string
          recorded_by?: string | null
          respiratory_rate?: number | null
          temperature?: number | null
          temperature_unit?: string | null
          updated_at?: string | null
          weight?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "vital_signs_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
        ]
      }
      workflow_instances: {
        Row: {
          completed_at: string | null
          context: Json
          created_at: string | null
          current_step: number | null
          id: string
          results: Json | null
          started_at: string | null
          status: Database["public"]["Enums"]["workflow_status"]
          updated_at: string | null
          workflow_id: string | null
        }
        Insert: {
          completed_at?: string | null
          context: Json
          created_at?: string | null
          current_step?: number | null
          id?: string
          results?: Json | null
          started_at?: string | null
          status?: Database["public"]["Enums"]["workflow_status"]
          updated_at?: string | null
          workflow_id?: string | null
        }
        Update: {
          completed_at?: string | null
          context?: Json
          created_at?: string | null
          current_step?: number | null
          id?: string
          results?: Json | null
          started_at?: string | null
          status?: Database["public"]["Enums"]["workflow_status"]
          updated_at?: string | null
          workflow_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "workflow_instances_workflow_id_fkey"
            columns: ["workflow_id"]
            isOneToOne: false
            referencedRelation: "workflows"
            referencedColumns: ["id"]
          },
        ]
      }
      workflow_logs: {
        Row: {
          created_at: string | null
          details: Json | null
          id: string
          message: string | null
          status: Database["public"]["Enums"]["workflow_status"]
          step_name: string
          step_number: number
          workflow_instance_id: string | null
        }
        Insert: {
          created_at?: string | null
          details?: Json | null
          id?: string
          message?: string | null
          status: Database["public"]["Enums"]["workflow_status"]
          step_name: string
          step_number: number
          workflow_instance_id?: string | null
        }
        Update: {
          created_at?: string | null
          details?: Json | null
          id?: string
          message?: string | null
          status?: Database["public"]["Enums"]["workflow_status"]
          step_name?: string
          step_number?: number
          workflow_instance_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "workflow_logs_workflow_instance_id_fkey"
            columns: ["workflow_instance_id"]
            isOneToOne: false
            referencedRelation: "workflow_instances"
            referencedColumns: ["id"]
          },
        ]
      }
      workflows: {
        Row: {
          created_at: string | null
          created_by: string | null
          description: string | null
          enabled: boolean | null
          id: string
          name: string
          organization_id: string | null
          steps: Json[]
          trigger_config: Json
          trigger_type: Database["public"]["Enums"]["workflow_trigger"]
          type: Database["public"]["Enums"]["workflow_type"]
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          enabled?: boolean | null
          id?: string
          name: string
          organization_id?: string | null
          steps: Json[]
          trigger_config: Json
          trigger_type: Database["public"]["Enums"]["workflow_trigger"]
          type: Database["public"]["Enums"]["workflow_type"]
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          enabled?: boolean | null
          id?: string
          name?: string
          organization_id?: string | null
          steps?: Json[]
          trigger_config?: Json
          trigger_type?: Database["public"]["Enums"]["workflow_trigger"]
          type?: Database["public"]["Enums"]["workflow_type"]
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "workflows_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      analytics_daily_appointments: {
        Row: {
          avg_duration: number | null
          cancelled_appointments: number | null
          completed_appointments: number | null
          date: string | null
          department_id: string | null
          organization_id: string | null
          total_appointments: number | null
        }
        Relationships: [
          {
            foreignKeyName: "appointments_department_id_fkey"
            columns: ["department_id"]
            isOneToOne: false
            referencedRelation: "departments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "appointments_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      analytics_patient_metrics: {
        Row: {
          active_patients: number | null
          avg_patient_age: number | null
          organization_id: string | null
          total_appointments: number | null
          total_medical_records: number | null
          total_medications: number | null
          total_patients: number | null
        }
        Relationships: [
          {
            foreignKeyName: "patients_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      analytics_provider_metrics: {
        Row: {
          medical_records_created: number | null
          medications_prescribed: number | null
          organization_id: string | null
          provider_id: string | null
          provider_name: string | null
          role: Database["public"]["Enums"]["user_role"] | null
          total_appointments: number | null
          unique_patients: number | null
        }
        Relationships: [
          {
            foreignKeyName: "healthcare_providers_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      secure_analytics_daily_appointments: {
        Row: {
          avg_duration: number | null
          cancelled_appointments: number | null
          completed_appointments: number | null
          date: string | null
          department_id: string | null
          organization_id: string | null
          total_appointments: number | null
        }
        Relationships: [
          {
            foreignKeyName: "appointments_department_id_fkey"
            columns: ["department_id"]
            isOneToOne: false
            referencedRelation: "departments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "appointments_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      secure_analytics_patient_metrics: {
        Row: {
          active_patients: number | null
          avg_patient_age: number | null
          organization_id: string | null
          total_appointments: number | null
          total_medical_records: number | null
          total_medications: number | null
          total_patients: number | null
        }
        Relationships: [
          {
            foreignKeyName: "patients_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      secure_analytics_provider_metrics: {
        Row: {
          medical_records_created: number | null
          medications_prescribed: number | null
          organization_id: string | null
          provider_id: string | null
          provider_name: string | null
          role: Database["public"]["Enums"]["user_role"] | null
          total_appointments: number | null
          unique_patients: number | null
        }
        Relationships: [
          {
            foreignKeyName: "healthcare_providers_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Functions: {
      accept_organization_invite: {
        Args: { invite_id: string }
        Returns: boolean
      }
      add_lab_results: {
        Args: {
          p_patient_id: string
          p_provider_id: string
          p_test_name: string
          p_results: Json
          p_normal_range: Json
          p_notes: string
        }
        Returns: string
      }
      add_medical_record: {
        Args: {
          p_patient_id: string
          p_provider_id: string
          p_chief_complaint: string
          p_diagnosis: string[]
          p_treatment_plan: string
          p_notes: string
          p_attachments?: Json
        }
        Returns: string
      }
      assign_task: {
        Args: {
          p_title: string
          p_description: string
          p_assigned_to: string
          p_priority?: Database["public"]["Enums"]["task_priority"]
          p_due_date?: string
          p_related_to?: Json
          p_metadata?: Json
        }
        Returns: string
      }
      can_access_analytics: {
        Args: { org_id: string }
        Returns: boolean
      }
      check_user_permission: {
        Args: {
          p_user_id: string
          p_resource: string
          p_action: string
          p_context?: Json
        }
        Returns: boolean
      }
      clean_expired_invites: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      decline_organization_invite: {
        Args: { invite_id: string }
        Returns: boolean
      }
      disable_rls: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      get_appointment_statistics: {
        Args: {
          p_provider_id?: string
          p_start_date?: string
          p_end_date?: string
        }
        Returns: {
          total_appointments: number
          completed_appointments: number
          cancelled_appointments: number
          avg_duration: number
          most_common_reason: string
        }[]
      }
      get_medication_statistics: {
        Args: { p_provider_id?: string; p_start_date?: string }
        Returns: {
          medication_name: string
          total_prescriptions: number
          active_prescriptions: number
          avg_duration_days: number
        }[]
      }
      get_organization_metrics: {
        Args: {
          p_organization_id: string
          p_start_date?: string
          p_end_date?: string
        }
        Returns: {
          metric_name: string
          current_value: number
          previous_value: number
          change_percentage: number
        }[]
      }
      get_patient_demographics: {
        Args: { p_provider_id?: string }
        Returns: {
          age_group: string
          gender_distribution: Json
          patient_count: number
        }[]
      }
      get_patient_summary: {
        Args: { p_patient_id: string }
        Returns: Json
      }
      get_patient_visit_history: {
        Args: {
          p_patient_id: string
          p_start_date?: string
          p_end_date?: string
        }
        Returns: {
          visit_date: string
          visit_type: string
          provider_name: string
          diagnosis: string[]
          medications_prescribed: string[]
          lab_tests_performed: string[]
        }[]
      }
      get_provider_workload: {
        Args: { p_start_date?: string; p_end_date?: string }
        Returns: {
          provider_id: string
          provider_name: string
          provider_type: Database["public"]["Enums"]["provider_type"]
          total_appointments: number
          total_patients: number
          avg_appointments_per_day: number
        }[]
      }
      get_user_departments: {
        Args: { p_user_id: string }
        Returns: {
          department_id: string
          department_name: string
        }[]
      }
      has_patient_access: {
        Args: { provider_id: string; patient_id: string }
        Returns: boolean
      }
      is_healthcare_provider: {
        Args: { user_id: string }
        Returns: boolean
      }
      is_patient: {
        Args: { user_id: string }
        Returns: boolean
      }
      mark_message_read: {
        Args: { p_message_id: string }
        Returns: boolean
      }
      mark_notification_read: {
        Args: { p_notification_id: string }
        Returns: boolean
      }
      prescribe_medication: {
        Args: {
          p_patient_id: string
          p_provider_id: string
          p_medication_name: string
          p_dosage: string
          p_frequency: string
          p_start_date: string
          p_end_date: string
          p_instructions: string
        }
        Returns: string
      }
      random_date: {
        Args: { start_date: string; end_date: string }
        Returns: string
      }
      random_timestamp: {
        Args: { start_timestamp: string; end_timestamp: string }
        Returns: string
      }
      record_analytics_event: {
        Args:
          | { p_event_type: string; p_event_data: Json }
          | { p_event_type: string; p_user_id: string; p_data: Json }
        Returns: string
      }
      record_analytics_metric: {
        Args: {
          p_metric_name: string
          p_metric_value: number
          p_dimensions?: Json
        }
        Returns: string
      }
      refresh_analytics_views: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      refresh_materialized_views: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      schedule_appointment: {
        Args: {
          p_patient_id: string
          p_provider_id: string
          p_appointment_date: string
          p_duration_minutes: number
          p_reason: string
        }
        Returns: string
      }
      search_medical_records: {
        Args: {
          search_query: string
          p_patient_id?: string
          p_provider_id?: string
        }
        Returns: {
          id: string
          patient_id: string
          provider_id: string
          visit_date: string
          chief_complaint: string
          diagnosis: string[]
          relevance: number
        }[]
      }
      search_patients: {
        Args: { search_query: string; p_provider_id?: string }
        Returns: {
          id: string
          first_name: string
          last_name: string
          date_of_birth: string
          gender: Database["public"]["Enums"]["gender"]
          relevance: number
        }[]
      }
      send_message: {
        Args: {
          p_conversation_id: string
          p_content: string
          p_attachments?: Json
          p_metadata?: Json
        }
        Returns: string
      }
      send_notification: {
        Args: {
          p_type: Database["public"]["Enums"]["notification_type"]
          p_recipient_id: string
          p_title: string
          p_content: string
          p_metadata?: Json
          p_priority?: Database["public"]["Enums"]["notification_priority"]
          p_scheduled_for?: string
        }
        Returns: string
      }
      start_conversation: {
        Args: {
          p_participants: string[]
          p_type?: Database["public"]["Enums"]["conversation_type"]
          p_title?: string
          p_initial_message?: string
          p_metadata?: Json
        }
        Returns: string
      }
      start_workflow: {
        Args: { p_workflow_id: string; p_context: Json }
        Returns: string
      }
      update_task_status: {
        Args: {
          p_task_id: string
          p_status: Database["public"]["Enums"]["task_status"]
          p_comment?: string
        }
        Returns: boolean
      }
      update_workflow_step: {
        Args: {
          p_instance_id: string
          p_step_number: number
          p_status: Database["public"]["Enums"]["workflow_status"]
          p_result?: Json
          p_message?: string
        }
        Returns: boolean
      }
    }
    Enums: {
      administration_route:
        | "intramuscular"
        | "subcutaneous"
        | "intradermal"
        | "oral"
        | "nasal"
      administration_site:
        | "left_arm"
        | "right_arm"
        | "left_thigh"
        | "right_thigh"
        | "other"
      alert_severity: "low" | "medium" | "high" | "critical"
      alert_status: "active" | "inactive" | "resolved"
      allergy_severity: "mild" | "moderate" | "severe" | "life_threatening"
      allergy_status: "active" | "inactive" | "resolved"
      appointment_status:
        | "scheduled"
        | "checked_in"
        | "in_progress"
        | "completed"
        | "cancelled"
        | "no_show"
      conversation_type: "direct" | "group" | "department" | "announcement"
      department_type:
        | "primary_care"
        | "pediatrics"
        | "cardiology"
        | "neurology"
        | "orthopedics"
        | "emergency"
        | "laboratory"
        | "pharmacy"
        | "radiology"
        | "billing"
        | "administration"
      gender: "male" | "female" | "other" | "prefer_not_to_say"
      material_format: "text" | "pdf" | "video" | "audio" | "interactive"
      message_state: "sent" | "delivered" | "read"
      notification_priority: "low" | "medium" | "high" | "urgent"
      notification_status: "pending" | "sent" | "delivered" | "read" | "failed"
      notification_type:
        | "appointment_reminder"
        | "lab_result"
        | "prescription_update"
        | "medical_record_update"
        | "task_assignment"
        | "message"
        | "alert"
        | "system_update"
      order_priority: "routine" | "urgent" | "stat" | "emergency"
      order_status:
        | "pending"
        | "approved"
        | "in_progress"
        | "completed"
        | "cancelled"
        | "declined"
      order_type:
        | "lab"
        | "imaging"
        | "medication"
        | "procedure"
        | "referral"
        | "consultation"
        | "other"
      provider_type: "doctor" | "nurse" | "specialist" | "admin"
      referral_priority: "routine" | "urgent" | "emergency"
      referral_status:
        | "pending"
        | "scheduled"
        | "completed"
        | "cancelled"
        | "declined"
      task_priority: "low" | "medium" | "high" | "urgent"
      task_status:
        | "pending"
        | "in_progress"
        | "completed"
        | "cancelled"
        | "blocked"
      user_role:
        | "system_admin"
        | "org_admin"
        | "clinical_admin"
        | "physician"
        | "nurse_practitioner"
        | "registered_nurse"
        | "medical_assistant"
        | "front_desk"
        | "billing_staff"
        | "pharmacist"
        | "lab_technician"
        | "patient"
      workflow_status:
        | "pending"
        | "in_progress"
        | "completed"
        | "cancelled"
        | "failed"
      workflow_trigger: "scheduled" | "event_based" | "manual"
      workflow_type:
        | "appointment_reminder"
        | "lab_result_notification"
        | "prescription_renewal"
        | "patient_followup"
        | "referral_management"
        | "insurance_verification"
        | "document_review"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      administration_route: [
        "intramuscular",
        "subcutaneous",
        "intradermal",
        "oral",
        "nasal",
      ],
      administration_site: [
        "left_arm",
        "right_arm",
        "left_thigh",
        "right_thigh",
        "other",
      ],
      alert_severity: ["low", "medium", "high", "critical"],
      alert_status: ["active", "inactive", "resolved"],
      allergy_severity: ["mild", "moderate", "severe", "life_threatening"],
      allergy_status: ["active", "inactive", "resolved"],
      appointment_status: [
        "scheduled",
        "checked_in",
        "in_progress",
        "completed",
        "cancelled",
        "no_show",
      ],
      conversation_type: ["direct", "group", "department", "announcement"],
      department_type: [
        "primary_care",
        "pediatrics",
        "cardiology",
        "neurology",
        "orthopedics",
        "emergency",
        "laboratory",
        "pharmacy",
        "radiology",
        "billing",
        "administration",
      ],
      gender: ["male", "female", "other", "prefer_not_to_say"],
      material_format: ["text", "pdf", "video", "audio", "interactive"],
      message_state: ["sent", "delivered", "read"],
      notification_priority: ["low", "medium", "high", "urgent"],
      notification_status: ["pending", "sent", "delivered", "read", "failed"],
      notification_type: [
        "appointment_reminder",
        "lab_result",
        "prescription_update",
        "medical_record_update",
        "task_assignment",
        "message",
        "alert",
        "system_update",
      ],
      order_priority: ["routine", "urgent", "stat", "emergency"],
      order_status: [
        "pending",
        "approved",
        "in_progress",
        "completed",
        "cancelled",
        "declined",
      ],
      order_type: [
        "lab",
        "imaging",
        "medication",
        "procedure",
        "referral",
        "consultation",
        "other",
      ],
      provider_type: ["doctor", "nurse", "specialist", "admin"],
      referral_priority: ["routine", "urgent", "emergency"],
      referral_status: [
        "pending",
        "scheduled",
        "completed",
        "cancelled",
        "declined",
      ],
      task_priority: ["low", "medium", "high", "urgent"],
      task_status: [
        "pending",
        "in_progress",
        "completed",
        "cancelled",
        "blocked",
      ],
      user_role: [
        "system_admin",
        "org_admin",
        "clinical_admin",
        "physician",
        "nurse_practitioner",
        "registered_nurse",
        "medical_assistant",
        "front_desk",
        "billing_staff",
        "pharmacist",
        "lab_technician",
        "patient",
      ],
      workflow_status: [
        "pending",
        "in_progress",
        "completed",
        "cancelled",
        "failed",
      ],
      workflow_trigger: ["scheduled", "event_based", "manual"],
      workflow_type: [
        "appointment_reminder",
        "lab_result_notification",
        "prescription_renewal",
        "patient_followup",
        "referral_management",
        "insurance_verification",
        "document_review",
      ],
    },
  },
} as const

