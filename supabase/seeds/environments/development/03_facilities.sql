-- Start transaction
BEGIN;

-- Create facilities for ALL organizations
DO $$
DECLARE
  org_record RECORD;
  facility_name TEXT;
  facility_type TEXT;
  street_address TEXT;
  city_name TEXT;
  phone_number TEXT;
  email_address TEXT;
  streets TEXT[] := ARRAY['Medical Way', 'Health Blvd', 'Wellness Ave', 'Care St', 'Hospital Dr', 'Clinic Rd', 'Center Ln', 'Plaza Way', 'Park Ave', 'Main St'];
  cities TEXT[] := ARRAY['Healthville', 'Careville', 'Medtown', 'Wellness City', 'Healthcare Heights', 'Medical Park', 'Clinic Grove', 'Hospital Hills', 'Care Valley', 'Health Springs'];
BEGIN
  -- Loop through each organization and create a facility
  FOR org_record IN
    SELECT id, name, type FROM organizations
  LOOP
    -- Generate facility name based on organization name
    facility_name := org_record.name;

    -- Set facility type based on organization type
    CASE org_record.type
      WHEN 'hospital' THEN facility_type := 'hospital';
      WHEN 'clinic' THEN facility_type := 'clinic';
      WHEN 'practice' THEN facility_type := 'specialty';
      ELSE facility_type := 'clinic';
    END CASE;

    -- Generate realistic address
    street_address := (100 + random() * 9899)::int || ' ' || streets[1 + (random() * (array_length(streets, 1) - 1))::int];
    city_name := cities[1 + (random() * (array_length(cities, 1) - 1))::int];

    -- Generate phone and email
    phone_number := '555-' || LPAD((100 + random() * 899)::int::text, 3, '0') || '-' || LPAD((1000 + random() * 8999)::int::text, 4, '0');
    email_address := 'info@' || lower(replace(split_part(org_record.name, ' ', 1), '''', '')) || '.example.com';

    -- Insert the facility
    INSERT INTO public.facilities (
      id,
      organization_id,
      name,
      type,
      address,
      contact_info,
      created_at,
      updated_at
    ) VALUES (
      uuid_generate_v4(),
      org_record.id,
      facility_name,
      facility_type,
      jsonb_build_object(
        'street', street_address,
        'city', city_name,
        'state', CASE (random() * 10)::int
          WHEN 0 THEN 'CA'
          WHEN 1 THEN 'TX'
          WHEN 2 THEN 'FL'
          WHEN 3 THEN 'NY'
          WHEN 4 THEN 'PA'
          WHEN 5 THEN 'IL'
          WHEN 6 THEN 'OH'
          WHEN 7 THEN 'GA'
          WHEN 8 THEN 'NC'
          ELSE 'MI'
        END,
        'zip', LPAD((10000 + random() * 89999)::int::text, 5, '0')
      ),
      jsonb_build_object(
        'phone', phone_number,
        'email', email_address,
        'fax', '555-' || LPAD((100 + random() * 899)::int::text, 3, '0') || '-' || LPAD((1000 + random() * 8999)::int::text, 4, '0')
      ),
      NOW() - (random() * interval '365 days'),
      NOW() - (random() * interval '30 days')
    );
  END LOOP;
END
$$;

-- Commit transaction
COMMIT;