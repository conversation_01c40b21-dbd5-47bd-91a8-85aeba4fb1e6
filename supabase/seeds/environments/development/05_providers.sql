-- Start transaction
BEGIN;

-- First, insert providers from existing auth users
WITH org_data AS (
  SELECT id, name FROM organizations
),
department_data AS (
  SELECT d.id, d.name, f.name as facility_name, f.organization_id
  FROM departments d
  JOIN facilities f ON d.facility_id = f.id
),
user_data AS (
  SELECT
    id,
    raw_user_meta_data->>'name' as name,
    split_part(raw_user_meta_data->>'name', ' ', 1) as first_name,
    split_part(raw_user_meta_data->>'name', ' ', 2) as last_name,
    raw_user_meta_data->>'organization' as org_name,
    email
  FROM auth.users
  WHERE email LIKE 'physician%@%' OR email LIKE 'nurse%@%'
),
provider_data AS (
  SELECT
    gen_random_uuid() as id,
    name,
    first_name,
    last_name,
    CASE
      WHEN email LIKE '%1@spritelymedical%' THEN 'Primary Care'
      WHEN email LIKE '%2@spritelymedical%' THEN 'Cardiology'
      WHEN email LIKE '%3@spritelymedical%' THEN 'Neurology'
      WHEN email LIKE '%1@spritelyclinic%' THEN 'Primary Care'
      WHEN email LIKE '%2@spritelyclinic%' THEN 'Pediatrics'
      WHEN email LIKE '%1@spritelypediatrics%' THEN 'Pediatrics'
      WHEN email LIKE '%1@valleycardiology%' THEN 'Cardiology'
      WHEN email LIKE '%1@northsideregional%' THEN 'Surgery'
      ELSE 'Primary Care'
    END as department_name,
    CASE
      WHEN org_name = 'Spritely Medical Center' THEN 'Spritely Main Hospital'
      WHEN org_name = 'Spritely Community Clinic' THEN 'Spritely Community Clinic'
      WHEN org_name = 'Spritely Pediatrics' THEN 'Spritely Pediatric Center'
      WHEN org_name = 'Valley Cardiology Associates' THEN 'Valley Cardiology Center'
      WHEN org_name = 'Northside Regional Hospital' THEN 'Northside Regional Hospital'
      ELSE 'Main Facility'
    END as facility_name,
    org_name,
    id as user_id,
    email
  FROM user_data
)

-- Insert Healthcare Providers from auth users
INSERT INTO public.healthcare_providers (
  id,
  organization_id,
  department_id,
  user_id,
  first_name,
  last_name,
  credentials,
  specialties,
  provider_type,
  role,
  created_at,
  updated_at
)
SELECT
  p.id,
  o.id as organization_id,
  d.id as department_id,
  p.user_id,
  p.first_name,
  p.last_name,
  CASE
    WHEN p.email LIKE 'physician%@%' THEN '{"degree": "MD", "certifications": ["Board Certified"]}'::jsonb
    WHEN p.email LIKE 'nurse%@%' THEN '{"degree": "NP", "certifications": ["Registered Nurse"]}'::jsonb
    ELSE '{"degree": "MD", "certifications": ["Board Certified"]}'::jsonb
  END as credentials,
  CASE
    WHEN p.department_name = 'Primary Care' THEN ARRAY['family medicine', 'preventive care']
    WHEN p.department_name = 'Cardiology' THEN ARRAY['cardiology', 'interventional cardiology']
    WHEN p.department_name = 'Neurology' THEN ARRAY['neurology', 'stroke care']
    WHEN p.department_name = 'Pediatrics' THEN ARRAY['pediatrics', 'adolescent medicine']
    WHEN p.department_name = 'Surgery' THEN ARRAY['surgery', 'trauma']
    ELSE ARRAY['general medicine']
  END as specialties,
  CASE
    WHEN p.email LIKE 'nurse%@%' THEN 'nurse'::provider_type
    ELSE 'doctor'::provider_type
  END as provider_type,
  CASE
    WHEN p.email LIKE 'nurse%@%' THEN 'registered_nurse'::user_role
    ELSE 'physician'::user_role
  END as role,
  NOW() - (random() * interval '90 days'),
  NOW() - (random() * interval '30 days')
FROM provider_data p
JOIN org_data o ON p.org_name = o.name
LEFT JOIN department_data d ON p.department_name = d.name AND p.facility_name = d.facility_name
WHERE NOT EXISTS (
  SELECT 1 FROM public.healthcare_providers hp WHERE hp.user_id = p.user_id
);

-- Now add additional providers for organizations that need more staff
-- This ensures every organization has adequate healthcare providers
DO $$
DECLARE
  org_record RECORD;
  dept_record UUID;
  provider_count INTEGER;
  target_count INTEGER;
  i INTEGER;
  first_names TEXT[] := ARRAY['Sarah', 'Michael', 'Jennifer', 'David', 'Lisa', 'Robert', 'Maria', 'James', 'Patricia', 'John', 'Linda', 'William', 'Elizabeth', 'Richard', 'Barbara', 'Thomas', 'Susan', 'Christopher', 'Jessica', 'Daniel', 'Karen', 'Matthew', 'Nancy', 'Anthony', 'Betty', 'Mark', 'Helen', 'Donald', 'Sandra', 'Steven', 'Donna', 'Paul', 'Carol', 'Andrew', 'Ruth', 'Joshua', 'Sharon', 'Kenneth', 'Michelle', 'Kevin', 'Laura', 'Brian', 'Sarah', 'George', 'Kimberly', 'Timothy', 'Deborah', 'Ronald', 'Dorothy', 'Jason', 'Lisa', 'Edward', 'Nancy', 'Jeffrey', 'Karen', 'Ryan', 'Betty', 'Jacob', 'Helen', 'Gary', 'Sandra'];
  last_names TEXT[] := ARRAY['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez', 'Wilson', 'Anderson', 'Thomas', 'Taylor', 'Moore', 'Jackson', 'Martin', 'Lee', 'Perez', 'Thompson', 'White', 'Harris', 'Sanchez', 'Clark', 'Ramirez', 'Lewis', 'Robinson', 'Walker', 'Young', 'Allen', 'King', 'Wright', 'Scott', 'Torres', 'Nguyen', 'Hill', 'Flores', 'Green', 'Adams', 'Nelson', 'Baker', 'Hall', 'Rivera', 'Campbell', 'Mitchell', 'Carter', 'Roberts'];
  specialties_list TEXT[] := ARRAY['family medicine', 'internal medicine', 'pediatrics', 'cardiology', 'neurology', 'orthopedics', 'dermatology', 'psychiatry', 'emergency medicine', 'radiology', 'anesthesiology', 'surgery', 'obstetrics', 'gynecology', 'oncology', 'endocrinology', 'gastroenterology', 'pulmonology', 'nephrology', 'rheumatology'];
BEGIN
  -- Loop through each organization
  FOR org_record IN
    SELECT id, name, type FROM organizations
  LOOP
    -- Count current providers
    SELECT COUNT(*) INTO provider_count
    FROM healthcare_providers
    WHERE organization_id = org_record.id;

    -- Set target based on organization type
    CASE org_record.type
      WHEN 'hospital' THEN target_count := 15;
      WHEN 'clinic' THEN target_count := 8;
      WHEN 'practice' THEN target_count := 5;
      ELSE target_count := 6;
    END CASE;

    -- Add providers if needed
    IF provider_count < target_count THEN
      -- Get a department for this organization
      SELECT d.id INTO dept_record
      FROM departments d
      JOIN facilities f ON d.facility_id = f.id
      WHERE f.organization_id = org_record.id
      ORDER BY random()
      LIMIT 1;

      -- Add providers
      FOR i IN 1..(target_count - provider_count) LOOP
        INSERT INTO public.healthcare_providers (
          id,
          organization_id,
          department_id,
          first_name,
          last_name,
          credentials,
          specialties,
          provider_type,
          role,
          created_at,
          updated_at
        ) VALUES (
          uuid_generate_v4(),
          org_record.id,
          dept_record,
          first_names[1 + (random() * (array_length(first_names, 1) - 1))::int],
          last_names[1 + (random() * (array_length(last_names, 1) - 1))::int],
          CASE
            WHEN random() < 0.8 THEN '{"degree": "MD", "certifications": ["Board Certified"]}'::jsonb
            ELSE '{"degree": "NP", "certifications": ["Registered Nurse"]}'::jsonb
          END,
          ARRAY[specialties_list[1 + (random() * (array_length(specialties_list, 1) - 1))::int]],
          CASE WHEN random() < 0.8 THEN 'doctor'::provider_type ELSE 'nurse'::provider_type END,
          CASE WHEN random() < 0.8 THEN 'physician'::user_role ELSE 'registered_nurse'::user_role END,
          NOW() - (random() * interval '90 days'),
          NOW() - (random() * interval '30 days')
        );
      END LOOP;
    END IF;
  END LOOP;
END
$$;

COMMIT;