-- Start transaction
BEGIN;

-- Reset auth schema users first
TRUNCATE auth.users CASCADE;

-- System Admin (access to all organizations)
INSERT INTO auth.users (
  instance_id,
  id,
  aud,
  role,
  email,
  encrypted_password,
  email_confirmed_at,
  recovery_sent_at,
  last_sign_in_at,
  raw_app_meta_data,
  raw_user_meta_data,
  created_at,
  updated_at,
  confirmation_token,
  email_change,
  email_change_token_new,
  recovery_token
)
VALUES
  ('00000000-0000-0000-0000-000000000000',
   '00000000-0000-0000-0000-000000000001',
   'authenticated',
   'authenticated',
   '<EMAIL>',
   crypt('password123', gen_salt('bf')),
   current_timestamp,
   current_timestamp,
   current_timestamp,
   '{"provider":"email","providers":["email"]}',
   jsonb_build_object(
     'name', 'System Administrator',
     'title', 'System Administrator',
     'phone', '************',
     'is_test', true
   ),
   current_timestamp,
   current_timestamp,
   '',
   '',
   '',
   '');

-- Create organization-specific users
WITH org_data AS (
  SELECT id, name FROM public.organizations
)
INSERT INTO auth.users (
  instance_id,
  id,
  aud,
  role,
  email,
  encrypted_password,
  email_confirmed_at,
  recovery_sent_at,
  last_sign_in_at,
  raw_app_meta_data,
  raw_user_meta_data,
  created_at,
  updated_at,
  confirmation_token,
  email_change,
  email_change_token_new,
  recovery_token
)
SELECT
  '00000000-0000-0000-0000-000000000000',
  uuid_generate_v4(),
  'authenticated',
  'authenticated',
  email,
  crypt('password123', gen_salt('bf')),
  current_timestamp,
  current_timestamp,
  current_timestamp,
  '{"provider":"email","providers":["email"]}',
  jsonb_build_object(
    'name', name,
    'title', title,
    'organization', org_name,
    'phone', phone,
    'is_test', true
  ),
  current_timestamp,
  current_timestamp,
  '',
  '',
  '',
  ''
FROM (
  -- Spritely Medical Center users
  SELECT '<EMAIL>', 'Jennifer Martinez', 'Hospital Administrator', 'Spritely Medical Center', '************'
  UNION ALL
  SELECT '<EMAIL>', 'Dr. Robert Chen', 'Chief of Medicine', 'Spritely Medical Center', '************'
  UNION ALL
  SELECT '<EMAIL>', 'Dr. Dr. Michael', 'Cardiologist', 'Spritely Medical Center', '************'
  UNION ALL
  SELECT '<EMAIL>', 'Dr. Dr. Lisa', 'Neurologist', 'Spritely Medical Center', '************'
  UNION ALL
  SELECT '<EMAIL>', 'Emily Rodriguez', 'Head Nurse', 'Spritely Medical Center', '************'
  UNION ALL
  SELECT '<EMAIL>', 'David Kim', 'Registered Nurse', 'Spritely Medical Center', '************'
  UNION ALL
  SELECT '<EMAIL>', 'Jessica Taylor', 'Front Desk Manager', 'Spritely Medical Center', '************'
  UNION ALL
  SELECT '<EMAIL>', 'Thomas Brown', 'Billing Specialist', 'Spritely Medical Center', '************'

  -- Spritely Community Clinic users
  UNION ALL
  SELECT '<EMAIL>', 'Amanda Wilson', 'Clinic Director', 'Spritely Community Clinic', '************'
  UNION ALL
  SELECT '<EMAIL>', 'Dr. James Thompson', 'Family Physician', 'Spritely Community Clinic', '************'
  UNION ALL
  SELECT '<EMAIL>', 'Dr. Lisa Garcia', 'Pediatrician', 'Spritely Community Clinic', '************'
  UNION ALL
  SELECT '<EMAIL>', 'Kevin Lee', 'Nurse Practitioner', 'Spritely Community Clinic', '************'
  UNION ALL
  SELECT '<EMAIL>', 'Sophia Martinez', 'Office Manager', 'Spritely Community Clinic', '************'

  -- Spritely Pediatrics users
  UNION ALL
  SELECT '<EMAIL>', 'Daniel Jackson', 'Practice Manager', 'Spritely Pediatrics', '************'
  UNION ALL
  SELECT '<EMAIL>', 'Dr. Elizabeth White', 'Pediatrician', 'Spritely Pediatrics', '************'
  UNION ALL
  SELECT '<EMAIL>', 'Maria Hernandez', 'Pediatric Nurse', 'Spritely Pediatrics', '************'
  UNION ALL
  SELECT '<EMAIL>', 'Christopher Davis', 'Receptionist', 'Spritely Pediatrics', '************'

  -- Northside Regional Hospital users
  UNION ALL
  SELECT '<EMAIL>', 'William Scott', 'Hospital CEO', 'Northside Regional Hospital', '************'
  UNION ALL
  SELECT '<EMAIL>', 'Dr. Michelle Lee', 'Chief of Surgery', 'Northside Regional Hospital', '************'
  UNION ALL
  SELECT '<EMAIL>', 'Brandon Turner', 'Nursing Director', 'Northside Regional Hospital', '************'
  UNION ALL
  SELECT '<EMAIL>', 'Rachel Green', 'HR Manager', 'Northside Regional Hospital', '************'

  -- Valley Cardiology Associates users
  UNION ALL
  SELECT '<EMAIL>', 'Patricia Moore', 'Practice Administrator', 'Valley Cardiology Associates', '************'
  UNION ALL
  SELECT '<EMAIL>', 'Dr. Richard Adams', 'Cardiologist', 'Valley Cardiology Associates', '************'
  UNION ALL
  SELECT '<EMAIL>', 'Nicole Wright', 'Cardiac Nurse', 'Valley Cardiology Associates', '************'
) as users(email, name, title, org_name, phone);

-- Create identities for all users
INSERT INTO auth.identities (
  id,
  user_id,
  identity_data,
  provider,
  provider_id,
  last_sign_in_at,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  id,
  jsonb_build_object(
    'sub', id::text,
    'email', email,
    'email_verified', true,
    'phone_verified', false
  ),
  'email',
  id, -- Use the user_id as the provider_id, not the email
  current_timestamp,
  current_timestamp,
  current_timestamp
FROM auth.users
WHERE NOT EXISTS (
  SELECT 1 FROM auth.identities WHERE user_id = auth.users.id
);

-- Map users to organizations with appropriate roles
WITH org_data AS (
  SELECT id, name FROM public.organizations
),
user_org_data AS (
  SELECT id, email, raw_user_meta_data->>'organization' as org_name
  FROM auth.users
  WHERE raw_user_meta_data->>'organization' IS NOT NULL OR email = '<EMAIL>'
)
INSERT INTO public.user_roles (
  id,
  user_id,
  organization_id,
  role,
  custom_permissions,
  created_at,
  updated_at,
  invitation_status
)
SELECT
  uuid_generate_v4(),
  u.id,
  CASE
    WHEN u.email = '<EMAIL>' THEN NULL -- System admin has no specific org
    ELSE (SELECT id FROM org_data WHERE name = u.org_name)
  END,
  CASE
    WHEN u.email = '<EMAIL>' THEN 'system_admin'
    WHEN u.email LIKE 'admin@%' THEN 'org_admin'
    WHEN u.email LIKE 'physician%@%' THEN 'physician'
    WHEN u.email LIKE 'nurse%@%' THEN 'registered_nurse'
    WHEN u.email LIKE 'staff%@%' THEN 'front_desk'
    WHEN u.email LIKE 'billing%@%' THEN 'billing_staff'
    ELSE 'front_desk'
  END::user_role,
  CASE
    WHEN u.email = '<EMAIL>' THEN '{"all": true, "multi_org": true}'
    ELSE '{}'
  END::jsonb,
  NOW(),
  NOW(),
  'accepted'
FROM user_org_data u;

-- For the system admin, also add them to each organization
WITH org_data AS (
  SELECT id FROM public.organizations
),
admin_user AS (
  SELECT id FROM auth.users WHERE email = '<EMAIL>'
)
INSERT INTO public.user_roles (
  id,
  user_id,
  organization_id,
  role,
  custom_permissions,
  created_at,
  updated_at,
  invitation_status
)
SELECT
  uuid_generate_v4(),
  admin_user.id,
  org_data.id,
  'system_admin'::user_role,
  '{"all": true}'::jsonb,
  NOW(),
  NOW(),
  'accepted'
FROM org_data, admin_user;

-- Commit transaction
COMMIT;