-- Reset all data in public schema
BEGIN;

-- Disable triggers temporarily
SET session_replication_role = 'replica';

-- Check if tables exist before truncating
DO $$
BEGIN
    -- Core entities
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'organizations') THEN
        EXECUTE 'TRUNCATE TABLE public.organizations CASCADE';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'facilities') THEN
        EXECUTE 'TRUNCATE TABLE public.facilities CASCADE';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'departments') THEN
        EXECUTE 'TRUNCATE TABLE public.departments CASCADE';
    END IF;

    -- Healthcare providers and patients
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'healthcare_providers') THEN
        EXECUTE 'TRUNCATE TABLE public.healthcare_providers CASCADE';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'patients') THEN
        EXECUTE 'TRUNCATE TABLE public.patients CASCADE';
    END IF;

    -- Clinical data
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'appointments') THEN
        EXECUTE 'TRUNCATE TABLE public.appointments CASCADE';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'medical_records') THEN
        EXECUTE 'TRUNCATE TABLE public.medical_records CASCADE';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'documents') THEN
        EXECUTE 'TRUNCATE TABLE public.documents CASCADE';
    END IF;

    -- Insurance and workflows
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'insurance') THEN
        EXECUTE 'TRUNCATE TABLE public.insurance CASCADE';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'workflows') THEN
        EXECUTE 'TRUNCATE TABLE public.workflows CASCADE';
    END IF;

    -- Teams and roles
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'teams') THEN
        EXECUTE 'TRUNCATE TABLE public.teams CASCADE';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'user_roles') THEN
        EXECUTE 'TRUNCATE TABLE public.user_roles CASCADE';
    END IF;

    -- Clinical details
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'allergies') THEN
        EXECUTE 'TRUNCATE TABLE public.allergies CASCADE';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'medications') THEN
        EXECUTE 'TRUNCATE TABLE public.medications CASCADE';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'vital_signs') THEN
        EXECUTE 'TRUNCATE TABLE public.vital_signs CASCADE';
    END IF;

    -- Orders and tasks
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'orders') THEN
        EXECUTE 'TRUNCATE TABLE public.orders CASCADE';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'tasks') THEN
        EXECUTE 'TRUNCATE TABLE public.tasks CASCADE';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'task_details') THEN
        EXECUTE 'TRUNCATE TABLE public.task_details CASCADE';
    END IF;

    -- Clinical notes and immunizations
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'clinical_notes') THEN
        EXECUTE 'TRUNCATE TABLE public.clinical_notes CASCADE';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'immunizations') THEN
        EXECUTE 'TRUNCATE TABLE public.immunizations CASCADE';
    END IF;

    -- Templates and notifications
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'notification_templates') THEN
        EXECUTE 'TRUNCATE TABLE public.notification_templates CASCADE';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'templates') THEN
        EXECUTE 'TRUNCATE TABLE public.templates CASCADE';
    END IF;

    -- Lab results and claims
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'lab_results') THEN
        EXECUTE 'TRUNCATE TABLE public.lab_results CASCADE';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'claims') THEN
        EXECUTE 'TRUNCATE TABLE public.claims CASCADE';
    END IF;

    -- Billing and care teams
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'billing_codes') THEN
        EXECUTE 'TRUNCATE TABLE public.billing_codes CASCADE';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'care_team_members') THEN
        EXECUTE 'TRUNCATE TABLE public.care_team_members CASCADE';
    END IF;

    -- Communication
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'conversations') THEN
        EXECUTE 'TRUNCATE TABLE public.conversations CASCADE';
    END IF;

    -- Education materials
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'education_materials') THEN
        EXECUTE 'TRUNCATE TABLE public.education_materials CASCADE';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'patient_education_records') THEN
        EXECUTE 'TRUNCATE TABLE public.patient_education_records CASCADE';
    END IF;

    -- Inventory
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'inventory') THEN
        EXECUTE 'TRUNCATE TABLE public.inventory CASCADE';
    END IF;

    -- Patient-specific settings
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'patient_alerts') THEN
        EXECUTE 'TRUNCATE TABLE public.patient_alerts CASCADE';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'patient_portal_settings') THEN
        EXECUTE 'TRUNCATE TABLE public.patient_portal_settings CASCADE';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'patient_questionnaires') THEN
        EXECUTE 'TRUNCATE TABLE public.patient_questionnaires CASCADE';
    END IF;

    -- Referrals and permissions
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'referrals') THEN
        EXECUTE 'TRUNCATE TABLE public.referrals CASCADE';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'role_permissions') THEN
        EXECUTE 'TRUNCATE TABLE public.role_permissions CASCADE';
    END IF;

    -- Workflow instances
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'workflow_instances') THEN
        EXECUTE 'TRUNCATE TABLE public.workflow_instances CASCADE';
    END IF;

    -- Notifications
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'notifications') THEN
        EXECUTE 'TRUNCATE TABLE public.notifications CASCADE';
    END IF;

    -- System tables
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'audit_logs') THEN
        EXECUTE 'TRUNCATE TABLE public.audit_logs CASCADE';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'analytics') THEN
        EXECUTE 'TRUNCATE TABLE public.analytics CASCADE';
    END IF;

END $$;

-- Re-enable triggers
SET session_replication_role = 'origin';

COMMIT;