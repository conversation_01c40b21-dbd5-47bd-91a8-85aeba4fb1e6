# Spritely Database Setup

This document provides instructions for setting up and seeding the Spritely healthcare database.

## Database Structure

The Spritely database consists of:
- Core healthcare entities (organizations, facilities, departments, providers, patients)
- Clinical data (medical records, clinical notes, allergies, medications, etc.)
- Administrative data (appointments, tasks, documents, etc.)
- System configuration (notification templates, teams, workflows, etc.)
- User authentication and authorization (via Supabase Auth)

## Setup Instructions

### Option 1: Manual Setup (Recommended)

For the most reliable setup, follow these steps:

1. Start the Supabase services:
   ```bash
   supabase start
   ```

2. Apply the schema and seed data separately:
   ```bash
   # First apply the schema
   psql postgresql://postgres:postgres@127.0.0.1:54322/postgres -f supabase/schema.sql

   # Then apply the seed data
   psql postgresql://postgres:postgres@127.0.0.1:54322/postgres -f supabase/seed.sql
   ```

   This approach ensures that the schema is fully applied before attempting to insert seed data.

### Option 2: Using Supabase CLI

The Supabase CLI should apply migrations and seeds automatically, but if you encounter issues:

1. Make sure your `config.toml` has the correct paths:
   ```toml
   [db.migrations]
   schema_paths = ["./schema.sql"]

   [db.seed]
   enabled = true
   sql_paths = ["./seed.sql"]
   ```

2. Run the reset command:
   ```bash
   supabase db reset
   ```

3. If you encounter errors, try the manual approach (Option 1).

## Seed Data Overview

The seed data includes:

### Auth Users
- **System Admin**: <EMAIL>
- **Providers**: <EMAIL>, <EMAIL>
- **Nurses**: <EMAIL>, <EMAIL>
- **Front Desk**: <EMAIL>
- **Billing**: <EMAIL>

All users have the password: `password123`

### Organizations
- Spritely Medical Center (hospital)
- Spritely Community Clinic (clinic)
- Spritely Pediatrics (specialty)

### Healthcare Providers
- 5 providers with different specialties

### Patients
- 5 patients with various demographics and medical conditions

### Clinical Data
- Medical records
- Clinical notes
- Allergies
- Medications
- Vital signs
- Immunizations
- Orders

### Administrative Data
- Appointments
- Tasks
- Documents
- Insurance providers

### System Configuration
- Notification templates
- Teams
- Templates
- Workflows

## Troubleshooting

If you encounter issues with the Supabase CLI:

1. Use the manual setup approach (Option 1) as it's the most reliable method.

2. Check for errors in the output and address them individually.

3. Verify the data was inserted correctly:
   ```bash
   # Check organizations
   psql postgresql://postgres:postgres@127.0.0.1:54322/postgres -c "SELECT COUNT(*) FROM organizations;"

   # Check auth users
   psql postgresql://postgres:postgres@127.0.0.1:54322/postgres -c "SELECT COUNT(*) FROM auth.users;"

   # Check user roles
   psql postgresql://postgres:postgres@127.0.0.1:54322/postgres -c "SELECT COUNT(*) FROM public.user_roles;"

   # Check healthcare providers
   psql postgresql://postgres:postgres@127.0.0.1:54322/postgres -c "SELECT COUNT(*) FROM public.healthcare_providers;"
   ```

4. If you need to reset the database completely:
   ```bash
   # Stop Supabase
   supabase stop

   # Remove the database volume
   docker volume rm spritely_db_data

   # Start Supabase again
   supabase start

   # Apply schema and seed data
   psql postgresql://postgres:postgres@127.0.0.1:54322/postgres -f supabase/schema.sql
   psql postgresql://postgres:postgres@127.0.0.1:54322/postgres -f supabase/seed.sql
   ```

## Using Snaplet (Future Enhancement)

For more advanced seeding capabilities, consider using Snaplet:

1. Install Snaplet:
   ```bash
   npm install -D postgres @snaplet/seed
   ```

2. Initialize Snaplet:
   ```bash
   npx @snaplet/seed init
   ```

3. Configure Snaplet to work with Supabase:
   ```typescript
   // seed.config.ts
   import { SeedPostgres } from "@snaplet/seed/adapter-postgres";
   import { defineConfig } from "@snaplet/seed/config";
   import postgres from "postgres";

   export default defineConfig({
     adapter: () => {
       const client = postgres("postgresql://postgres:postgres@127.0.0.1:54322/postgresql");
       return new SeedPostgres(client);
     },
     select: [
       "!*",
       "public*",
       "auth.users",
       "auth.identities",
       "auth.sessions",
     ]
   });
   ```

4. Sync Snaplet with your database:
   ```bash
   npx @snaplet/seed sync
   ```

5. Generate seed data:
   ```bash
   npx tsx seed.ts > supabase/seed.sql
   ```
