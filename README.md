# Spritely - Healthcare Management System

Spritely is a comprehensive healthcare management system built with Supa<PERSON>, React, and React Native, organized as a monorepo using Turborepo.

## Project Structure

This Turborepo includes the following packages/apps:

### Apps and Packages

- `web`: a React application for browser access ([README](./apps/web/README.md))
- `mobile`: a React Native application for mobile access ([README](./apps/mobile/README.md))
- `supabase-types`: TypeScript definitions generated from the Supabase database schema ([README](./packages/supabase-types/README.md))
- `supabase`: Supabase client configuration and utilities ([README](./packages/supabase/README.md))
- `eslint-config`: shared ESLint configurations ([README](./packages/eslint-config/README.md))
- `typescript-config`: shared TypeScript configurations ([README](./packages/typescript-config/README.md))

Each package/app is 100% [TypeScript](https://www.typescriptlang.org/).

## Getting Started (New Developers)

### Prerequisites

You'll need access to:
1. GitHub repository (ask your admin for access)
2. 1Password workspace (ask your admin for an invitation)

### One-Command Setup

After cloning the repository, run:

```bash
# Install dependencies
npm install

# Set up development environment
npm run dev:setup
```

This will:
1. Check and install required tools (1Password CLI, Supabase CLI)
2. Guide you through 1Password authentication
3. Set up your local development environment
4. Start Supabase locally
5. Generate TypeScript types

Once setup is complete, you can start the development server:

```bash
npm run dev:full
```

## Environment Configuration

Spritely supports multiple Supabase environments:

- **Local**: For local development with a local Supabase instance
- **Development**: For development with a cloud Supabase instance
- **Staging**: For testing with a staging Supabase instance
- **Production**: For production with the production Supabase instance

#### Option A: Local Development

The recommended way to set up Supabase locally:

```sh
# Set up local environment
npm run env:local

# Start Supabase and seed the database
npm run supabase:setup

# Start the development server
npm run dev:full
```

This will:
1. Configure your environment for local development
2. Start a local Supabase instance
3. Apply the database schema
4. Seed the database with test data
5. Start the development server

#### Option B: Cloud Development

To connect to a cloud Supabase instance:

```sh
# For development environment
npm run env:development
npm run dev:development

# For staging environment
npm run env:staging
npm run dev:staging

# For production environment
npm run env:production
npm run dev:production
```

These commands will:
1. Securely retrieve credentials from 1Password
2. Configure your environment files
3. Start the development server connected to the specified environment

#### Branch-Specific Configuration

The project is configured with branch-specific database seeding:

- `dev` branch → Development environment
- `staging` branch → Staging environment
- `master` branch → Production environment

When you push to these branches, the appropriate environment's schema and seed data will be applied.

### 2. Test Users for Login

The seed data includes several test users that you can use to log in to the application:

| Email                   | Password      | Role          | Description                                |
|-------------------------|---------------|--------------|--------------------------------------------|
| <EMAIL>   | password123   | Admin        | System administrator with full access      |
| <EMAIL> | password123 | Provider     | Healthcare provider with clinical access   |
| <EMAIL>  | password123   | Nurse        | Nurse with patient care access            |
| <EMAIL> | password123 | Staff        | Front desk staff with scheduling access   |

The seed data also includes:
- Organizations, facilities, and departments
- Healthcare providers and patients
- Medical records, clinical notes, and other clinical data

#### Creating Custom Test Users

If you need to create custom test users with specific roles:

```sh
# Run the script to create custom test users
node create-test-user.js
```

**Note:** This script requires Supabase to be running properly with Docker.

### 3. Generate TypeScript Types

Generate TypeScript types from your local Supabase schema:

```sh
# Generate types
npm run generate-types
```

For more details on using these types, see the [supabase-types README](./packages/supabase-types/README.md).

### 4. Environment Configuration

The project uses environment-specific configuration files that are generated securely:

```sh
# Set up environment configuration
npm run env:local        # For local development
npm run env:development  # For development environment
npm run env:staging      # For staging environment
npm run env:production   # For production environment
```

These commands use the `scripts/setup-environment.sh` script which:

1. For local development:
   - Creates environment files with local Supabase configuration

2. For cloud environments (development, staging, production):
   - Uses 1Password CLI to securely retrieve credentials
   - Creates environment files with the appropriate Supabase configuration
   - Sets environment-specific variables

#### Security Considerations

- Environment files (`.env`) are not committed to version control
- Sensitive credentials are stored in 1Password
- The 1Password CLI is used to retrieve credentials securely
- Each environment has its own set of credentials

#### 1Password Setup

The project uses 1Password for secure credential management:

```sh
# Set up 1Password vault and items
npm run 1password:setup

# Organize the vault and add credentials
npm run 1password:organize
```

The organization script will guide you through:
1. Adding Supabase credentials for all environments
2. Creating Netlify deployment credentials
3. Adding GitHub repository credentials
4. Setting up 1Password service account for CI/CD

All items will be properly tagged and organized in the "Spritely" vault.

For detailed instructions on the 1Password setup and CI/CD integration, see the [CI/CD Setup Guide](./docs/ci-cd-setup.md).

### 5. Install Dependencies

```sh
npm install
```

### 6. Start Development Server

You can start the development server in different modes depending on your environment:

```sh
# Start development server only (no Supabase)
npm run dev

# Start with local Supabase
npm run dev:full

# Start with specific environment configurations
npm run dev:development  # Development environment
npm run dev:staging      # Staging environment
npm run dev:production   # Production environment
```

The different commands do the following:

- `dev:full`: Runs the `supabase:setup` task to set up local Supabase, then starts the development server
- `dev:development`: Sets up development environment configuration, then starts the development server
- `dev:staging`: Sets up staging environment configuration, then starts the development server
- `dev:production`: Sets up production environment configuration, then starts the development server

All tasks are integrated into the Turbo repo menu system, so you can see the status of each task in the Turbo UI.

### 7. Supabase Cloud Operations

The project includes several npm scripts for working with Supabase Cloud environments:

```sh
# Link to specific environment Supabase projects
npm run supabase:link:development
npm run supabase:link:staging
npm run supabase:link:production

# Push local schema and seed data to the linked environment
npm run supabase:push

# Generate TypeScript types from specific environments
npm run generate-types:development
npm run generate-types:staging
npm run generate-types:production
```

#### Environment-Specific Seed Data

The project uses environment-specific seed data located in:

- `supabase/seeds/environments/development/` - Development environment seeds
- `supabase/seeds/environments/staging/` - Staging environment seeds
- `supabase/seeds/environments/production/` - Production environment seeds
- `supabase/seeds/common/` - Common seed data for all environments

See the [seeds README](./supabase/seeds/README.md) for more details on the seed data structure.

#### CI/CD Integration

The project includes GitHub Actions workflows for CI/CD:

- Automatically deploys database changes to the appropriate Supabase environment
- Builds and deploys the application to the appropriate hosting environment
- Securely manages credentials using 1Password

Branch-specific deployments:
- `dev` branch → Development environment
- `staging` branch → Staging environment
- `master` branch → Production environment

For detailed instructions on the CI/CD setup, see the [CI/CD Setup Guide](./docs/ci-cd-setup.md).

## Authentication and Login

Spritely uses Supabase Auth for user authentication. The authentication flow works as follows:

1. Users sign in at `/login` with their email and password
2. After successful authentication:
   - If the user has no organization, they are redirected to `/setup` to create one
   - If the user has an organization, they are redirected to `/dashboard`
3. Users can sign out from the user menu or the setup page

### Authentication Routes

- `/login` - Sign in page
- `/signup` - Registration page (if enabled)
- `/setup` - Organization setup page (only accessible when logged in)
- `/dashboard` - Main application dashboard (requires authentication and organization)

## Database Schema

The database includes the following main tables:

- `users`: User authentication and profile information
- `organizations`: Organizations that users belong to
- `user_roles`: User roles within organizations
- `healthcare_providers`: Details about doctors and other healthcare providers
- `patients`: Patient information
- `appointments`: Scheduled patient appointments
- `medical_records`: Patient medical history and visit information
- `medications`: Patient medication tracking

The database implements Row Level Security (RLS) policies to ensure proper data access control based on user roles and relationships.

For more details on the database structure and types, see the [supabase-types README](./packages/supabase-types/README.md).

## Project Documentation

For more detailed information about specific parts of the project, refer to the README files in each package:

### Applications
- [Web Application](./apps/web/README.md)
- [Mobile Application](./apps/mobile/README.md)

### Packages
- [Supabase Types](./packages/supabase-types/README.md)
- [Supabase Integration](./packages/supabase/README.md)
- [ESLint Configuration](./packages/eslint-config/README.md)
- [TypeScript Configuration](./packages/typescript-config/README.md)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

This project is proprietary. Usage of this code is restricted to authorized personnel only.
