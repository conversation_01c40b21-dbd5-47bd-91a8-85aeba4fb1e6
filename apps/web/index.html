<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/src/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Spritely Healthcare</title>
    <style>
      /* Recovery UI styles */
      .recovery-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100vh;
        text-align: center;
        font-family: sans-serif;
        padding: 1rem;
        background-color: #f9fafb;
      }
      .recovery-card {
        max-width: 500px;
        width: 100%;
        padding: 2rem;
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
      }
      .recovery-title {
        color: #3b82f6;
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 1rem;
      }
      .recovery-button {
        background: #3b82f6;
        color: white;
        border: none;
        padding: 0.5rem 1.5rem;
        border-radius: 0.25rem;
        cursor: pointer;
        font-weight: 600;
        margin-top: 1rem;
        transition: background-color 0.2s;
      }
      .recovery-button:hover {
        background: #2563eb;
      }
      .recovery-secondary-button {
        background: #e5e7eb;
        color: #374151;
        border: none;
        padding: 0.5rem 1.5rem;
        border-radius: 0.25rem;
        cursor: pointer;
        font-weight: 600;
        margin-top: 0.5rem;
        transition: background-color 0.2s;
      }
      .recovery-secondary-button:hover {
        background: #d1d5db;
      }
    </style>
    <script>
      // Track application state for recovery
      window.spritely = window.spritely || {
        appLoaded: false,
        reactMounted: false,
        recoveryAttempted: false,
        startTime: Date.now()
      };
      
      // Function to create recovery UI
      function createRecoveryUI(message, isError = false) {
        // Don't show recovery UI if it was already attempted
        if (window.spritely.recoveryAttempted) return;
        window.spritely.recoveryAttempted = true;
        
        // Check if app has rendered already
        const root = document.getElementById('root');
        const appDiv = document.getElementById('app');
        
        if ((root && root.children.length > 1) || (appDiv && appDiv.children.length > 0)) {
          // App has content, don't show recovery UI
          return;
        }
        
        const loadingTime = Math.round((Date.now() - window.spritely.startTime) / 1000);
        
        const recoveryDiv = document.createElement('div');
        recoveryDiv.className = 'recovery-container';
        recoveryDiv.id = 'recovery-ui';
        recoveryDiv.innerHTML = `
          <div class="recovery-card">
            <h2 class="recovery-title">${isError ? 'Application Error' : 'Loading Taking Too Long'}</h2>
            <p style="margin-bottom: 1rem;">${message}</p>
            <p style="margin-bottom: 1rem; font-size: 0.875rem; color: #6b7280;">
              Loading time: ${loadingTime} seconds
            </p>
            <div style="display: flex; flex-direction: column; gap: 0.5rem;">
              <button onclick="window.location.reload()" class="recovery-button">
                Reload Application
              </button>
              <button onclick="clearCacheAndReload()" class="recovery-secondary-button">
                Clear Cache & Reload
              </button>
              <button onclick="window.location.href='/login'" class="recovery-secondary-button">
                Return to Login
              </button>
            </div>
          </div>
        `;
        
        // Append the recovery UI
        if (root) {
          // First clear the root to remove any partial UI
          if (!isError) {
            root.innerHTML = '';
          }
          root.appendChild(recoveryDiv);
        } else {
          document.body.appendChild(recoveryDiv);
        }
      }
      
      // Function to clear cache and reload
      function clearCacheAndReload() {
        try {
          // Clear localStorage
          localStorage.clear();
          // Clear sessionStorage
          sessionStorage.clear();
          console.info('Cache cleared, reloading application...');
        } catch (e) {
          console.warn('Error clearing cache:', e);
        }
        
        // Force a hard reload
        window.location.reload(true);
      }
      
      // Global error handler
      window.addEventListener('error', function(event) {
        console.error('Global error caught:', event.error);
        
        if (!window.spritely.appLoaded) {
          // Create and show recovery UI for errors during initial load
          createRecoveryUI('Sorry, an error occurred while loading the application.', true);
        }
      });
      
      // Recovery check - if app doesn't render in 5 seconds, show a recovery UI
      setTimeout(function() {
        if (!window.spritely.appLoaded) {
          createRecoveryUI('The application is taking longer than expected to load. This might be due to a slow connection or an issue with the application.');
        }
      }, 5000);
      
      // Longer recovery check - show a more urgent message after 15 seconds
      setTimeout(function() {
        if (!window.spritely.appLoaded && !window.spritely.recoveryAttempted) {
          createRecoveryUI('The application appears to be stuck. Try clearing your browser cache or using a different browser.');
        }
      }, 15000);
      
      // Mark application as loaded when the main script loads
      window.addEventListener('DOMContentLoaded', function() {
        setTimeout(function() {
          window.spritely.appLoaded = true;
        }, 500);
      });
    </script>
  </head>
  <body>
    <div id="root">
      <!-- App will be mounted here -->
      <noscript>
        <div class="recovery-container">
          <div class="recovery-card">
            <h2 class="recovery-title">JavaScript Required</h2>
            <p>This application requires JavaScript to run.</p>
            <p>Please enable JavaScript in your browser settings and reload the page.</p>
          </div>
        </div>
      </noscript>
    </div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
