import { useAuth } from '@/hooks/useAuth';
import { useUserRoles } from '@/hooks/useUserRoles';
import { useMemo } from 'react';

/**
 * Hook to handle organization filtering logic for dashboard queries
 * 
 * For system admins in "All Organizations" mode, returns null (no filter)
 * For specific organization selection, returns the organization ID to filter by
 * For regular users, always returns their organization ID
 */
export function useOrganizationFilter() {
  const { organization } = useAuth();
  const { isSystemAdmin, organizationIds } = useUserRoles();

  const filterConfig = useMemo(() => {
    // If no organization is set, don't filter
    if (!organization) {
      return {
        shouldFilter: false,
        organizationId: null,
        organizationIds: [],
        isAllOrganizations: false
      };
    }

    // Check if this is the "All Organizations" mode for system admins
    const isAllOrganizations = organization.id === 'system-admin-no-org';

    if (isAllOrganizations && isSystemAdmin) {
      // System admin viewing all organizations - don't filter by organization_id
      return {
        shouldFilter: false,
        organizationId: null,
        organizationIds: organizationIds, // All orgs the user has access to
        isAllOrganizations: true
      };
    }

    // Regular organization selection - filter by the specific organization
    return {
      shouldFilter: true,
      organizationId: organization.id,
      organizationIds: [organization.id],
      isAllOrganizations: false
    };
  }, [organization, isSystemAdmin, organizationIds]);

  return filterConfig;
}

/**
 * Apply organization filter to a Supabase query
 * 
 * @param query - The Supabase query builder
 * @param filterConfig - The filter configuration from useOrganizationFilter
 * @returns The query with organization filter applied (or not)
 */
export function applyOrganizationFilter<T>(
  query: T,
  filterConfig: ReturnType<typeof useOrganizationFilter>
): T {
  if (!filterConfig.shouldFilter) {
    // Don't apply any organization filter
    return query;
  }

  // Apply the organization filter
  // Note: This assumes the query has an .eq method (Supabase query builder)
  return (query as any).eq('organization_id', filterConfig.organizationId);
}
