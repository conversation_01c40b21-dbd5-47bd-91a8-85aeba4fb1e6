import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';
import { Database } from '@spritely/supabase-types';
import { useEffect, useState } from 'react';

// Base appointment type from Supabase
type AppointmentBase = Database['public']['Tables']['appointments']['Row'];

// Extended appointment type with joined data and computed fields
export interface Appointment extends AppointmentBase {
  // Joined data
  patient?: {
    id: string;
    first_name: string;
    last_name: string;
  } | null;
  provider?: {
    id: string;
    first_name: string;
    last_name: string;
    provider_type?: string;
  } | null;
  // Computed fields
  time?: string;
  type?: string;
  doctor?: string;
  patient_name?: string;
}

export interface UseAppointmentsOptions {
  limit?: number;
  todayOnly?: boolean;
  upcomingOnly?: boolean;
  pastOnly?: boolean;
  providerId?: string;
  patientId?: string;
}

export function useAppointments(options: UseAppointmentsOptions = {}) {
  const { organization } = useAuth();
  const organizationFilter = useOrganizationFilter();
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [totalCount, setTotalCount] = useState(0);

  useEffect(() => {
    const fetchAppointments = async () => {
      if (!organization) {
        setAppointments([]);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        // Start building the query
        let query = supabase
          .from('appointments')
          .select(`
            *,
            patient:patients(id, first_name, last_name),
            provider:healthcare_providers(id, first_name, last_name, provider_type)
          `, { count: 'exact' });

        // Apply organization filter
        query = applyOrganizationFilter(query, organizationFilter);

        // Apply filters based on options
        if (options.todayOnly) {
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          const tomorrow = new Date(today);
          tomorrow.setDate(tomorrow.getDate() + 1);

          query = query
            .gte('appointment_date', today.toISOString())
            .lt('appointment_date', tomorrow.toISOString());
        }

        if (options.upcomingOnly) {
          const now = new Date();
          query = query.gte('appointment_date', now.toISOString());
        }

        if (options.pastOnly) {
          const now = new Date();
          query = query.lt('appointment_date', now.toISOString());
        }

        if (options.providerId) {
          query = query.eq('provider_id', options.providerId);
        }

        if (options.patientId) {
          query = query.eq('patient_id', options.patientId);
        }

        // Apply limit if specified
        if (options.limit) {
          query = query.limit(options.limit);
        }

        // Order by appointment date
        query = query.order('appointment_date', { ascending: true });

        const { data, error: fetchError, count } = await query;

        if (fetchError) throw new Error(fetchError.message);

        // Process the data to add computed fields
        const processedAppointments = data.map((appointment: Database['public']['Tables']['appointments']['Row'] & {
          patient?: { id: string; first_name: string; last_name: string } | null;
          provider?: { id: string; first_name: string; last_name: string; provider_type?: string } | null;
        }) => {
          const appointmentDate = new Date(appointment.appointment_date);

          return {
            ...appointment,
            time: appointmentDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            type: appointment.reason || 'Consultation',
            doctor: appointment.provider ?
              `Dr. ${appointment.provider.first_name} ${appointment.provider.last_name}` :
              'Unassigned',
            patient_name: appointment.patient ?
              `${appointment.patient.first_name} ${appointment.patient.last_name}` :
              'Unknown Patient'
          };
        });

        setAppointments(processedAppointments);
        if (count !== null) setTotalCount(count);
        setIsLoading(false);
      } catch (err) {
        console.error('Error fetching appointments:', err);
        setError(err instanceof Error ? err : new Error('Failed to fetch appointments'));
        setIsLoading(false);
      }
    };

    fetchAppointments();
  }, [organization, organizationFilter, options.limit, options.todayOnly, options.upcomingOnly, options.pastOnly, options.providerId, options.patientId]);

  return { appointments, isLoading, error, totalCount };
}
