import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';
import { Database } from '@spritely/supabase-types';
import { useEffect, useState } from 'react';
import { useOrganizationFilter, applyOrganizationFilter } from './useOrganizationFilter';

// Define the appointment type from the appointments table
type AppointmentRecord = Database['public']['Tables']['appointments']['Row'];


export interface Patient {
  id: string;
  first_name: string;
  last_name: string;
  date_of_birth: string;
  gender: string;
  phone: string | null;
  email: string | null;
  created_at: string | null;
  updated_at: string | null;
  organization_id: string;
  // Joined data
  appointments?: AppointmentRecord[];
  // Computed fields
  age?: number;
  full_name?: string;
  last_visit?: string | null;
  status?: string;
  avatar?: string;
}

export interface UsePatientOptions {
  limit?: number;
  recentOnly?: boolean;
}

export function usePatients(options: UsePatientOptions = {}) {
  const { organization } = useAuth();
  const organizationFilter = useOrganizationFilter();
  const [patients, setPatients] = useState<Patient[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [totalCount, setTotalCount] = useState(0);

  useEffect(() => {
    const fetchPatients = async () => {
      if (!organization) {
        setPatients([]);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        // Start building the query
        let query = supabase
          .from('patients')
          .select(`
            *,
            appointments:appointments(
              id,
              appointment_date,
              status
            )
          `, { count: 'exact' })
          .eq('organization_id', organization.id);

        // Apply limit if specified
        if (options.limit) {
          query = query.limit(options.limit);
        }

        // Order by most recent first
        query = query.order('created_at', { ascending: false });

        const { data, error: fetchError, count } = await query;

        if (fetchError) throw new Error(fetchError.message);

        // Process the data to add computed fields
        // Using a more specific type for better type safety
        const processedPatients = data.map((patient: Database['public']['Tables']['patients']['Row'] & {
          appointments?: Array<{
            id: string;
            appointment_date: string;
            status: string;
          }>;
        }) => {
          // Calculate age from date of birth
          const dob = new Date(patient.date_of_birth);
          const ageDifMs = Date.now() - dob.getTime();
          const ageDate = new Date(ageDifMs);
          const age = Math.abs(ageDate.getUTCFullYear() - 1970);

          // Get the most recent appointment date
          let lastVisit = null;
          let status = 'New Patient';

          if (patient.appointments && patient.appointments.length > 0) {
            // Sort appointments by date (most recent first)
            const sortedAppointments = [...patient.appointments].sort(
              (a, b) => new Date(b.appointment_date).getTime() - new Date(a.appointment_date).getTime()
            );

            if (sortedAppointments[0]) {
              lastVisit = sortedAppointments[0].appointment_date;

              // Determine status based on most recent appointment
              const mostRecent = sortedAppointments[0];
              if (mostRecent.status === 'completed') {
                status = 'Follow-up';
              } else if (mostRecent.status === 'scheduled') {
                status = 'Upcoming Appointment';
              } else if (mostRecent.status === 'cancelled') {
                status = 'Cancelled Appointment';
              }
            }
          }

          // Create a properly typed Patient object
          const typedPatient: Patient = {
            id: patient.id,
            first_name: patient.first_name,
            last_name: patient.last_name,
            date_of_birth: patient.date_of_birth,
            gender: patient.gender,
            phone: patient.phone,
            email: patient.email,
            created_at: patient.created_at,
            updated_at: patient.updated_at,
            organization_id: patient.organization_id,
            // Add computed fields
            age,
            full_name: `${patient.first_name} ${patient.last_name}`,
            last_visit: lastVisit,
            status,
            avatar: '',
            // Omit appointments from the typed patient object since we've already
            // extracted the information we need (last_visit and status)
            appointments: undefined
          };

          return typedPatient;
        });

        setPatients(processedPatients);
        if (count !== null) setTotalCount(count);
        setIsLoading(false);
      } catch (err) {
        console.error('Error fetching patients:', err);
        setError(err instanceof Error ? err : new Error('Failed to fetch patients'));
        setIsLoading(false);
      }
    };

    fetchPatients();
  }, [organization, options.limit, options.recentOnly]);

  return { patients, isLoading, error, totalCount };
}
