import { Dashboard } from '@/components/dashboard/Dashboard';
import { LoadingScreen } from '@/components/ui/loading-screen';
import { useAuth } from '@/hooks/useAuth';
import { fetchOrganizationData } from '@/lib/auth/organization-service';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

export function DashboardPage() {
  const { user, organization, session, setOrganization } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  // Ensure organization is loaded when navigating to dashboard
  useEffect(() => {
    const loadOrganization = async () => {
      if (!user || !session) {
        setIsLoading(false);
        return;
      }

      try {
        // If we already have an organization, no need to fetch
        if (organization) {
          setIsLoading(false);
          return;
        }

        // Fetch organization data
        const org = await fetchOrganizationData(user, session);
        
        if (org) {
          // Set the organization in context
          setOrganization(org);
        } else {
          // If no organization found, redirect to setup
          navigate('/setup');
        }
      } catch (error) {
        console.error('Error loading organization data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadOrganization();
  }, [user, session, organization, setOrganization, navigate]);

  // Show loading screen while checking organization
  if (isLoading) {
    return <LoadingScreen message="Loading your dashboard..." />;
  }

  // If no organization and not loading, redirect to setup
  if (!organization && !isLoading) {
    navigate('/setup');
    return <LoadingScreen message="Redirecting to setup..." />;
  }

  // Render the dashboard component
  return <Dashboard />;
}
