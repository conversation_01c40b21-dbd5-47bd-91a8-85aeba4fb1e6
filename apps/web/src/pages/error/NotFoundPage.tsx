import { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';

export function NotFoundPage() {
  const { user, organization } = useAuth();
  const navigate = useNavigate();
  
  const navigateWithLog = useCallback((to: string) => {
    if (process.env.NODE_ENV === 'development') {
      console.info(`404 navigation: to=${to}`);
    }
    navigate(to);
  }, [navigate]);
  
  const handleClearCacheAndReload = () => {
    try {
      // Clear any cached data that might be causing issues
      localStorage.removeItem('spritely_org_cache');
      sessionStorage.clear();
      console.info('Cache cleared, reloading page...');
    } catch (error) {
      console.warn('Error clearing cache:', error);
    }
    // Force a hard reload to clear any React state
    window.location.reload();
  };
  
  return (
    <div className="flex h-screen items-center justify-center bg-gray-50">
      <div className="w-full max-w-md p-8 bg-white rounded-lg shadow-md">
        <h1 className="text-2xl font-bold text-red-600 mb-4">404 - Page Not Found</h1>
        
        <div className="mb-6 text-gray-700">
          <p>The page you're looking for doesn't exist or you don't have access to it.</p>
        </div>
        
        <div className="bg-gray-100 p-4 rounded-lg mb-6">
          <p className="font-semibold mb-2">Debug Information:</p>
          <ul className="text-sm text-gray-700 space-y-1">
            <li>Current Path: <span className="font-mono">{window.location.pathname}</span></li>
            <li>User Authenticated: <span className="font-medium">{user ? 'Yes' : 'No'}</span></li>
            <li>Has Organization: <span className="font-medium">{organization ? 'Yes' : 'No'}</span></li>
          </ul>
        </div>
        
        <div className="flex flex-col gap-3">
          {user ? (
            <button
              onClick={() => navigateWithLog('/dashboard')}
              className="w-full py-2 px-4 bg-blue-600 text-white rounded hover:bg-blue-700 transition"
            >
              Go to Dashboard
            </button>
          ) : (
            <button
              onClick={() => navigateWithLog('/login')}
              className="w-full py-2 px-4 bg-blue-600 text-white rounded hover:bg-blue-700 transition"
            >
              Go to Login
            </button>
          )}
          
          <button
            onClick={() => window.location.reload()}
            className="w-full py-2 px-4 bg-gray-500 text-white rounded hover:bg-gray-600 transition"
          >
            Reload Page
          </button>
          
          <button
            onClick={handleClearCacheAndReload}
            className="w-full py-2 px-4 bg-yellow-500 text-white rounded hover:bg-yellow-600 transition"
          >
            Clear Cache & Reload
          </button>
        </div>
      </div>
    </div>
  );
} 