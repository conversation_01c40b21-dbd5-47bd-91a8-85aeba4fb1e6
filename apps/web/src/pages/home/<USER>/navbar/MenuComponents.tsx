import { X } from "lucide-react";
import { NavigationMenuTrigger } from "@/components/ui/navigation-menu";
import { cn } from "@/lib/utils";
import { forwardRef, useEffect, useRef } from "react";

type HoverTriggerProps = { 
  id: string; 
  label: string;
  activeItem: string | null;
  setActiveItem: (id: string) => void;
};

export const HoverTrigger = forwardRef<HTMLButtonElement, HoverTriggerProps>(
  ({ id, label, activeItem, setActiveItem }, ref) => {
    const isActive = activeItem === id;
    const triggerRef = useRef<HTMLButtonElement | null>(null);
    
    // Handle ref forwarding
    useEffect(() => {
      if (triggerRef.current && typeof ref === 'function') {
        ref(triggerRef.current);
      } else if (triggerRef.current && ref && 'current' in ref) {
        ref.current = triggerRef.current;
      }
    }, [ref]);
    
    return (
      <NavigationMenuTrigger
        ref={triggerRef}
        className={cn(
          "px-4 py-2 rounded-md text-sm font-medium transition-colors relative",
          "hover:bg-blue-100/70 dark:hover:bg-blue-800/40",
          "data-[state=open]:bg-blue-100/90 dark:data-[state=open]:bg-blue-800/50",
          "hover:text-foreground dark:hover:text-foreground",
          isActive ? "active-trigger" : ""
        )}
        onPointerEnter={() => setActiveItem(id)}
        onClick={(e) => {
          e.preventDefault();
          setActiveItem(id);
        }}
        data-menu-id={id}
      >
        {label}
      </NavigationMenuTrigger>
    )
  }
);
HoverTrigger.displayName = "HoverTrigger";

type MenuCloseButtonProps = { 
  onClick: () => void;
};

export const MenuCloseButton = ({ onClick }: MenuCloseButtonProps) => (
  <button 
    onClick={onClick}
    className="p-1 rounded-full hover:bg-accent/80 dark:hover:bg-blue-600/30"
    aria-label="Close menu"
  >
    <X className="w-5 h-5 text-muted-foreground" />
    <span className="sr-only">Close menu</span>
  </button>
); 