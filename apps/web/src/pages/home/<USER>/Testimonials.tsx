import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent } from "@/components/ui/card";
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel";
import Autoplay from "embla-carousel-autoplay";
import { useRef } from "react";

const testimonials = [
  {
    name: "Dr. <PERSON>",
    role: "Chief of Medicine",
    image: "https://images.pexels.com/photos/5452293/pexels-photo-5452293.jpeg?w=100&h=100&fit=crop",
    quote: "This EHR system has transformed our practice. The AI-powered features save us hours each day, allowing us to focus more on patient care."
  },
  {
    name: "Dr. <PERSON>",
    role: "Family Practice Director",
    image: "https://images.pexels.com/photos/5452201/pexels-photo-5452201.jpeg?w=100&h=100&fit=crop",
    quote: "The intuitive interface and smart automation have significantly improved our workflow efficiency. Our staff adapted quickly and love using it."
  },
  {
    name: "Dr. <PERSON>",
    role: "Pediatric Specialist",
    image: "https://images.pexels.com/photos/5452268/pexels-photo-5452268.jpeg?w=100&h=100&fit=crop",
    quote: "The pediatric-specific features and customizable templates have made documentation so much easier. It's clearly designed with physicians in mind."
  },
  {
    name: "Dr. <PERSON> <PERSON>",
    role: "Hospital Administrator",
    image: "https://images.pexels.com/photos/5452291/pexels-photo-5452291.jpeg?w=100&h=100&fit=crop",
    quote: "From an administrative perspective, the analytics and reporting features provide invaluable insights for improving hospital operations."
  },
  {
    name: "Dr. Rebecca Thompson",
    role: "Cardiologist",
    image: "https://images.pexels.com/photos/4173251/pexels-photo-4173251.jpeg?w=100&h=100&fit=crop",
    quote: "The cardiology-specific templates and decision support tools have greatly enhanced the quality of care we provide to our patients."
  },
  {
    name: "Dr. Kevin Park",
    role: "Orthopedic Surgeon",
    image: "https://images.pexels.com/photos/5327585/pexels-photo-5327585.jpeg?w=100&h=100&fit=crop",
    quote: "The surgical planning features and image integration are exceptional. It's streamlined our pre-operative workflow tremendously."
  },
  {
    name: "Dr. Lisa Martinez",
    role: "Primary Care Physician",
    image: "https://images.pexels.com/photos/5207104/pexels-photo-5207104.jpeg?w=100&h=100&fit=crop",
    quote: "As a PCP managing hundreds of patients, the population health tools have helped me identify at-risk patients I might have missed otherwise."
  },
  {
    name: "Dr. Thomas Wright",
    role: "Medical Director",
    image: "https://images.pexels.com/photos/5407206/pexels-photo-5407206.jpeg?w=100&h=100&fit=crop",
    quote: "The reporting dashboard gives me crucial insights into our clinic's performance and has helped us optimize our operations significantly."
  }
];

function TestimonialCard({ testimonial }: { testimonial: typeof testimonials[0] }) {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center gap-4 mb-4">
          <Avatar>
            <AvatarImage src={testimonial.image} />
            <AvatarFallback>{testimonial.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
          </Avatar>
          <div>
            <h4 className="font-semibold">{testimonial.name}</h4>
            <p className="text-sm text-muted-foreground">{testimonial.role}</p>
          </div>
        </div>
        <p className="text-muted-foreground italic">
          "{testimonial.quote}"
        </p>
      </CardContent>
    </Card>
  );
}

export function Testimonials() {
  const autoplayPlugin = useRef(
    Autoplay({ delay: 10000, stopOnInteraction: true })
  );

  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center mb-16 bg-gradient-to-r from-emerald-500 to-blue-600 bg-clip-text text-transparent">
          Trusted by Healthcare Leaders
        </h2>
        <Carousel 
          className="w-full max-w-5xl mx-auto" 
          plugins={[autoplayPlugin.current]}
          opts={{
            align: "start",
            loop: true,
          }}
        >
          <CarouselContent>
            {testimonials.map((testimonial, index) => (
              <CarouselItem key={index} className="md:basis-1/2 lg:basis-1/3 pl-4">
                <div className="p-1">
                  <TestimonialCard testimonial={testimonial} />
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
          <div className="flex justify-center mt-8">
            <CarouselPrevious className="static mr-2 translate-y-0" />
            <CarouselNext className="static ml-2 translate-y-0" />
          </div>
        </Carousel>
      </div>
    </section>
  );
} 