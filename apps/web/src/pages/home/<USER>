import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';

// The optimized home page doesn't need to do redirects anymore
// as the NavigationManager will handle that automatically
export function HomePage() {
  const { user } = useAuth();
  const [message, setMessage] = useState('Welcome to Spritely');
  
  // Just update the user-facing message based on auth state
  useEffect(() => {
    if (user) {
      setMessage('Welcome to Spritely');
    } else {
      setMessage('Please log in to continue');
    }
  }, [user]);
  
  return (
    <div className="flex h-screen items-center justify-center">
      <div className="text-center">
        <h2 className="text-xl font-semibold mb-2">Spritely Healthcare</h2>
        <p className="text-gray-500 mb-4">{message}</p>
        
        {process.env.NODE_ENV === 'development' && (
          <div className="p-4 mt-4 bg-gray-100 rounded text-sm">
            <p>Debug: Auth Status: {user ? 'Authenticated' : 'Not Authenticated'}</p>
            <p>Expected redirect by NavigationManager: {user ? '/dashboard' : '/login'}</p>
            <p>Current path: {window.location.pathname}</p>
          </div>
        )}
      </div>
    </div>
  );
} 