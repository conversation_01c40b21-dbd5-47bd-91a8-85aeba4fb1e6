// Extremely simplified setup page to debug rendering issues
export function SimpleSetupPage() {
  return (
    <div className="flex h-screen items-center justify-center p-4 bg-white">
      <div className="w-full max-w-md p-8 bg-blue-100 rounded-lg shadow-md">
        <h1 className="text-2xl font-bold mb-6 text-blue-800">Organization Setup</h1>
        
        <p className="text-gray-700 mb-6">
          This is a simplified setup page for debugging. The original page wasn't rendering properly.
        </p>
        
        <div className="flex flex-col gap-4">
          <button 
            className="py-3 px-4 bg-green-600 hover:bg-green-700 text-white rounded transition text-lg font-medium shadow-md"
            onClick={() => {
              const name = prompt('Enter organization name:', 'My Organization');
              if (name) {
                alert(`Creating organization: ${name}`);
                // In a real implementation, we would create the organization here
                // and then redirect to the dashboard
              }
            }}
          >
            Create Your First Organization
          </button>
          
          <div className="text-center">
            <p className="text-sm text-gray-500 mt-2">
              Or <a href="/login" className="text-blue-600 hover:underline">sign out</a> to switch accounts
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
