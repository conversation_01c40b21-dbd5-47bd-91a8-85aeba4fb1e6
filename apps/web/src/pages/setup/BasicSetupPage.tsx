import { useEffect } from 'react';

export function BasicSetupPage() {
  console.log('BasicSetupPage component is rendering');
  
  // Add a simple effect to log when the component mounts
  useEffect(() => {
    console.log('BasicSetupPage mounted');
    
    // Try to clear any cached data that might be causing issues
    try {
      localStorage.removeItem('spritely_org_cache');
      console.log('Cleared organization cache');
    } catch (e) {
      console.error('Error clearing cache:', e);
    }
    
    return () => console.log('BasicSetupPage unmounted');
  }, []);

  // Return a very simple component that should definitely render
  return (
    <div className="flex h-screen items-center justify-center p-4 bg-blue-100">
      <div className="w-full max-w-md p-8 bg-white rounded-lg shadow-lg">
        <h1 className="text-2xl font-bold mb-6 text-blue-800">Basic Setup Page</h1>
        
        <p className="text-gray-700 mb-6">
          This is a very basic setup page that should render without any dependencies.
        </p>
        
        <div className="flex flex-col gap-4">
          <button 
            className="py-3 px-4 bg-green-600 hover:bg-green-700 text-white rounded transition text-lg font-medium shadow-md"
            onClick={() => {
              alert('Button clicked!');
            }}
          >
            Test Button
          </button>
          
          <div className="text-center">
            <p className="text-sm text-gray-500 mt-2">
              Or <a href="/login" className="text-blue-600 hover:underline">sign out</a> to switch accounts
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
