// A completely standalone setup page that doesn't rely on any context or hooks
export function StandaloneSetupPage() {
  console.log('StandaloneSetupPage is rendering');
  
  return (
    <div style={{
      display: 'flex',
      height: '100vh',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '1rem',
      backgroundColor: '#EBF8FF'
    }}>
      <div style={{
        width: '100%',
        maxWidth: '28rem',
        padding: '2rem',
        backgroundColor: 'white',
        borderRadius: '0.5rem',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
      }}>
        <h1 style={{
          fontSize: '1.5rem',
          fontWeight: 'bold',
          marginBottom: '1.5rem',
          color: '#2C5282'
        }}>
          Standalone Setup Page
        </h1>
        
        <p style={{
          color: '#4A5568',
          marginBottom: '1.5rem'
        }}>
          This is a completely standalone setup page that doesn't rely on any context or hooks.
          It should render even if there are issues with the auth context.
        </p>
        
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '1rem'
        }}>
          <button 
            style={{
              padding: '0.75rem 1rem',
              backgroundColor: '#48BB78',
              color: 'white',
              border: 'none',
              borderRadius: '0.25rem',
              fontSize: '1.125rem',
              fontWeight: '500',
              cursor: 'pointer',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
            }}
            onClick={() => {
              alert('Button clicked!');
            }}
          >
            Test Button
          </button>
          
          <div style={{
            textAlign: 'center'
          }}>
            <p style={{
              fontSize: '0.875rem',
              color: '#718096',
              marginTop: '0.5rem'
            }}>
              Or <a 
                href="/login" 
                style={{
                  color: '#3182CE',
                  textDecoration: 'none'
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.textDecoration = 'underline';
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.textDecoration = 'none';
                }}
              >
                sign out
              </a> to switch accounts
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
