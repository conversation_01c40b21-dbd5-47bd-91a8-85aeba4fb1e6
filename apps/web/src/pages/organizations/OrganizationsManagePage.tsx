import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useAuth } from "@/hooks/useAuth";
import { useUserRoles } from "@/hooks/useUserRoles";
import { canManageOrganization, isSystemAdmin } from "@/lib/permissions/organization-permissions";
import { supabase } from "@/lib/supabase";
import { Json } from "@spritely/supabase-types";
import { Building2, Edit, Plus, Search, Users } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

interface OrganizationWithStats {
  id: string;
  name: string;
  type: string;
  owner_id: string;
  subscription_tier: string;
  created_at: string;
  updated_at: string;
  settings: Json | null;
  user_count: number;
  patient_count: number;
  user_role: string;
}

export function OrganizationsManagePage() {
  const { user } = useAuth();
  const { roles } = useUserRoles();
  const navigate = useNavigate();
  const [organizations, setOrganizations] = useState<OrganizationWithStats[]>([]);
  const [filteredOrganizations, setFilteredOrganizations] = useState<OrganizationWithStats[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");

  const userIsSystemAdmin = isSystemAdmin(roles.map(role => ({ role })));

  const fetchOrganizations = useCallback(async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      let query = supabase.from('organizations').select(`
        id,
        name,
        type,
        owner_id,
        subscription_tier,
        created_at,
        updated_at,
        settings
      `);

      // If not system admin, only fetch organizations user has access to
      if (!userIsSystemAdmin) {
        const { data: userRoles } = await supabase
          .from('user_roles')
          .select('organization_id')
          .eq('user_id', user.id);

        if (userRoles && userRoles.length > 0) {
          const orgIds = userRoles.map(role => role.organization_id).filter(Boolean);
          query = query.in('id', orgIds);
        } else {
          setOrganizations([]);
          setFilteredOrganizations([]);
          setIsLoading(false);
          return;
        }
      }

      const { data: orgsData, error } = await query.order('name');

      if (error) throw error;

      // Fetch additional stats for each organization
      const orgsWithStats = await Promise.all(
        (orgsData || []).map(async (org) => {
          // Get user count
          const { count: userCount } = await supabase
            .from('user_roles')
            .select('*', { count: 'exact', head: true })
            .eq('organization_id', org.id);

          // Get patient count
          const { count: patientCount } = await supabase
            .from('patients')
            .select('*', { count: 'exact', head: true })
            .eq('organization_id', org.id);

          // Get user's role in this organization
          const { data: userRole } = await supabase
            .from('user_roles')
            .select('role')
            .eq('user_id', user.id)
            .eq('organization_id', org.id)
            .single();

          return {
            ...org,
            settings: org.settings as Json | null,
            user_count: userCount || 0,
            patient_count: patientCount || 0,
            user_role: userRole?.role || 'none'
          } as OrganizationWithStats;
        })
      );

      setOrganizations(orgsWithStats);
      setFilteredOrganizations(orgsWithStats);
    } catch (error) {
      console.error('Error fetching organizations:', error);
      toast.error('Failed to load organizations');
    } finally {
      setIsLoading(false);
    }
  }, [user, userIsSystemAdmin]);

  useEffect(() => {
    fetchOrganizations();
  }, [fetchOrganizations]);

  // Filter organizations based on search term
  useEffect(() => {
    if (!searchTerm) {
      setFilteredOrganizations(organizations);
    } else {
      const filtered = organizations.filter(org =>
        org.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        org.type.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredOrganizations(filtered);
    }
  }, [searchTerm, organizations]);

  const canManageOrg = (orgId: string) => {
    const roleData = roles.map(role => ({ role, organization_id: orgId }));
    return canManageOrganization(roleData, orgId, user?.id);
  };

  const handleEditOrganization = (orgId: string) => {
    navigate(`/organizations/${orgId}/settings`);
  };

  const handleCreateOrganization = () => {
    navigate('/setup/organization');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'system_admin': return 'bg-red-100 text-red-800';
      case 'org_admin': return 'bg-blue-100 text-blue-800';
      case 'clinical_admin': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="container py-8 max-w-7xl">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">Manage Organizations</h1>
          <p className="text-muted-foreground mt-1">
            {userIsSystemAdmin ? 'Manage all organizations in the system' : 'Manage your organizations'}
          </p>
        </div>
        <Button onClick={handleCreateOrganization}>
          <Plus className="mr-2 h-4 w-4" /> Create Organization
        </Button>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Organizations ({filteredOrganizations.length})
            </CardTitle>
            <div className="relative w-72">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search organizations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center py-12">
              <div className="flex flex-col items-center gap-2">
                <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
                <p className="text-sm text-muted-foreground">Loading organizations...</p>
              </div>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Organization</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Your Role</TableHead>
                  <TableHead>Users</TableHead>
                  <TableHead>Patients</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredOrganizations.map((org) => (
                  <TableRow key={org.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Building2 className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <div className="font-medium">{org.name}</div>
                          <div className="text-sm text-muted-foreground">{org.id}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{org.type}</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={getRoleColor(org.user_role || 'none')}>
                        {org.user_role?.replace('_', ' ') || 'None'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        {org.user_count}
                      </div>
                    </TableCell>
                    <TableCell>{org.patient_count}</TableCell>
                    <TableCell>{formatDate(org.created_at)}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {(canManageOrg(org.id) || userIsSystemAdmin) && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditOrganization(org.id)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}

          {filteredOrganizations.length === 0 && !isLoading && (
            <div className="text-center py-12">
              <Building2 className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">
                {searchTerm ? 'No organizations found' : 'No organizations available'}
              </h3>
              <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                {searchTerm 
                  ? 'Try adjusting your search terms.'
                  : 'You don\'t have access to any organizations yet.'
                }
              </p>
              {!searchTerm && (
                <Button onClick={handleCreateOrganization}>
                  <Plus className="mr-2 h-4 w-4" /> Create Organization
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
