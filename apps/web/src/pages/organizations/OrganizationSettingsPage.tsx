import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useAuth } from "@/hooks/useAuth";
import { supabase } from "@/lib/supabase";
import { zodResolver } from "@hookform/resolvers/zod";
import type { Database } from "@spritely/supabase-types";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";
import { z } from "zod";

type Organization = Database['public']['Tables']['organizations']['Row'];

const organizationSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  type: z.string().min(1, "Please select an organization type"),
});

type OrganizationForm = z.infer<typeof organizationSchema>;

export function OrganizationSettingsPage() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { orgId } = useParams();
  const [isLoading, setIsLoading] = useState(true);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [orgData, setOrgData] = useState<Organization | null>(null);

  const form = useForm<OrganizationForm>({
    resolver: zodResolver(organizationSchema),
    defaultValues: {
      name: "",
      type: "",
    },
  });

  useEffect(() => {
    const fetchOrgData = async () => {
      if (!user || !orgId) return;

      try {
        setIsLoading(true);

        // Fetch organization data
        const { data: org, error: orgError } = await supabase
          .from('organizations')
          .select('*')
          .eq('id', orgId)
          .single();

        if (orgError) throw orgError;

        // Fetch user's role in the organization
        const { data: roleData, error: roleError } = await supabase
          .from('user_roles')
          .select('role')
          .eq('user_id', user.id)
          .eq('organization_id', orgId)
          .single();

        if (roleError) throw roleError;

        setOrgData(org);
        setUserRole(roleData.role);
        form.reset({
          name: org.name,
          type: org.type,
        });
      } catch (error) {
        console.error('Error fetching organization data:', error);
        toast.error('Failed to load organization settings');
        navigate('/organizations');
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrgData();
  }, [user, orgId, form, navigate]);

  const onSubmit = async (data: OrganizationForm) => {
    if (!orgData || !['system_admin', 'org_admin'].includes(userRole || '')) {
      return;
    }

    try {
      const { error } = await supabase
        .from('organizations')
        .update({
          name: data.name,
          type: data.type,
          settings: {
            ...(orgData.settings as Record<string, unknown> || {}),
            lastUpdated: new Date().toISOString(),
            lastUpdatedBy: user?.id,
          },
        })
        .eq('id', orgId);

      if (error) throw error;

      toast.success('Organization settings updated successfully');
      navigate('/organizations');
    } catch (error) {
      console.error('Error updating organization:', error);
      toast.error('Failed to update organization settings');
    }
  };

  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  if (!['system_admin', 'org_admin'].includes(userRole || '')) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardHeader>
            <CardTitle>Unauthorized</CardTitle>
            <CardDescription>
              You don't have permission to manage this organization's settings.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => navigate('/organizations')}>
              Back to Organizations
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-2xl py-8">
      <Card>
        <CardHeader>
          <CardTitle>Organization Settings</CardTitle>
          <CardDescription>
            Manage your organization's settings and configuration
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Organization Name</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Organization Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select organization type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="healthcare">Healthcare</SelectItem>
                        <SelectItem value="clinic">Clinic</SelectItem>
                        <SelectItem value="hospital">Hospital</SelectItem>
                        <SelectItem value="practice">Medical Practice</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex justify-end gap-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => navigate('/organizations')}
                >
                  Cancel
                </Button>
                <Button type="submit">
                  Save Changes
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}