// This file helps fix React 18.3 type incompatibilities
import React from 'react';

// Override ReactNode to ensure it's compatible across different React versions
declare module 'react' {
  interface ReactElement<P = any, T extends string | JSXElementConstructor<any> = string | JSXElementConstructor<any>> {
    type: T;
    props: P;
    key: Key | null;
  }

  type ReactNode = React.ReactElement | string | number | boolean | null | undefined | Iterable<ReactNode>;
} 