import { Database } from '@spritely/supabase-types';

// Define types from the Supabase database schema
export type UserRole = Database['public']['Enums']['user_role'];
export type Organization = Database['public']['Tables']['organizations']['Row'];
export type UserRoleRecord = Database['public']['Tables']['user_roles']['Row'];
export type HealthcareProvider = Database['public']['Tables']['healthcare_providers']['Row'];
export type Department = Database['public']['Tables']['departments']['Row'];
export type NotificationPreference = Database['public']['Tables']['notification_preferences']['Row'];
export type NotificationType = Database['public']['Enums']['notification_type'];

// Define settings-specific types
export interface UserSettings {
  profile: UserProfile;
  notifications: NotificationSettings;
  appearance: AppearanceSettings;
  security: SecuritySettings;
}

export interface UserProfile {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  title?: string;
  department?: Department;
  organization?: Organization;
  role?: UserRole;
  avatar?: string;
  bio?: string;
}

export interface NotificationSettings {
  email: boolean;
  push: boolean;
  sms: boolean;
  inApp: boolean;
  preferences: {
    [key in NotificationType]?: {
      enabled: boolean;
      channels: ('email' | 'push' | 'sms' | 'inApp')[];
    };
  };
}

export interface AppearanceSettings {
  theme: 'light' | 'dark' | 'system';
  compactMode: boolean;
  reducedMotion: boolean;
  fontSize: 'small' | 'medium' | 'large';
  colorScheme: 'default' | 'blue' | 'green' | 'purple';
}

export interface SecuritySettings {
  twoFactorEnabled: boolean;
  lastPasswordChange?: Date;
  sessionTimeout: number; // in minutes
  loginHistory: LoginHistoryEntry[];
}

export interface LoginHistoryEntry {
  timestamp: Date;
  ipAddress: string;
  device: string;
  browser: string;
  location?: string;
  successful: boolean;
}
