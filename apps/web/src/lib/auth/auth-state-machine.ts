import { Organization } from '@/contexts/auth-context-types';
import { User } from '@supabase/supabase-js';

// Define all possible auth states
export type AuthState =
  | { status: 'initializing' }
  | { status: 'unauthenticated' }
  | { status: 'authenticatedNoOrg'; user: User }
  | { status: 'authenticated'; user: User; organization: Organization };

// Define possible auth events
export type AuthEvent =
  | { type: 'INITIALIZE' }
  | { type: 'SIGN_IN'; user: User }
  | { type: 'LOAD_ORGANIZATION'; organization: Organization }
  | { type: 'SIGN_OUT' }
  | { type: 'ERROR'; error: Error };

// State machine transition function
export function authStateReducer(state: AuthState, event: AuthEvent): AuthState {
  switch (state.status) {
    case 'initializing':
      switch (event.type) {
        case 'SIGN_IN':
          return { status: 'authenticatedNoOrg', user: event.user };
        case 'SIGN_OUT':
          return { status: 'unauthenticated' };
        default:
          return state;
      }

    case 'unauthenticated':
      switch (event.type) {
        case 'SIGN_IN':
          return { status: 'authenticatedNoOrg', user: event.user };
        default:
          return state;
      }

    case 'authenticatedNoOrg':
      switch (event.type) {
        case 'LOAD_ORGANIZATION':
          return {
            status: 'authenticated',
            user: state.user,
            organization: event.organization
          };
        case 'SIGN_OUT':
          return { status: 'unauthenticated' };
        default:
          return state;
      }

    case 'authenticated':
      switch (event.type) {
        case 'LOAD_ORGANIZATION':
          return {
            status: 'authenticated',
            user: state.user,
            organization: event.organization
          };
        case 'SIGN_OUT':
          return { status: 'unauthenticated' };
        default:
          return state;
      }

    default:
      return state;
  }
}

// Helper for checking if user is authenticated in any state
export function isAuthenticated(state: AuthState): boolean {
  return state.status === 'authenticatedNoOrg' || state.status === 'authenticated';
}

// Helper for checking if we have organization data
export function hasOrganization(state: AuthState): boolean {
  return state.status === 'authenticated';
}

// Helper for getting user from any state
export function getUser(state: AuthState): User | null {
  if (state.status === 'authenticatedNoOrg' || state.status === 'authenticated') {
    return state.user;
  }
  return null;
}

// Helper for getting organization from any state
export function getOrganization(state: AuthState): Organization | null {
  if (state.status === 'authenticated') {
    return state.organization;
  }
  return null;
}