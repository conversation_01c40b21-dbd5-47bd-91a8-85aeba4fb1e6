/**
 * Organization permission utilities
 * 
 * This module provides utilities for checking organization-level permissions
 * based on user roles and custom permissions.
 */

export interface UserRoleData {
  role: string;
  custom_permissions?: Record<string, unknown>;
  organization_id?: string;
}

/**
 * Check if a user can manage a specific organization
 * 
 * @param userRoles - Array of user roles with permissions
 * @param organizationId - ID of the organization to check
 * @param userId - ID of the user (for owner checks)
 * @param organizationOwnerId - ID of the organization owner
 * @returns boolean indicating if user can manage the organization
 */
export function canManageOrganization(
  userRoles: UserRoleData[],
  organizationId: string,
  userId?: string,
  organizationOwnerId?: string
): boolean {
  // Find the user's role for this specific organization
  const userRole = userRoles.find(role => role.organization_id === organizationId);
  if (!userRole) return false;

  // System admins can manage any organization
  if (userRole.role === 'system_admin') return true;

  // Organization owners can manage their own organization
  if (userId && organizationOwnerId && userId === organizationOwnerId) return true;

  // Check for explicit organization management permissions
  if (userRole.role === 'org_admin') {
    const permissions = userRole.custom_permissions || {};
    return permissions.manage_organization === true || permissions.all === true;
  }

  return false;
}

/**
 * Check if a user is a system administrator
 * 
 * @param userRoles - Array of user roles
 * @returns boolean indicating if user is a system admin
 */
export function isSystemAdmin(userRoles: UserRoleData[]): boolean {
  return userRoles.some(role => role.role === 'system_admin');
}

/**
 * Check if a user can manage all organizations (system-wide management)
 * 
 * @param userRoles - Array of user roles with permissions
 * @returns boolean indicating if user can manage all organizations
 */
export function canManageAllOrganizations(userRoles: UserRoleData[]): boolean {
  return userRoles.some(role => {
    if (role.role === 'system_admin') return true;
    
    const permissions = role.custom_permissions || {};
    return permissions.manage_all_organizations === true || permissions.all === true;
  });
}

/**
 * Get organizations that a user can manage
 * 
 * @param userRoles - Array of user roles with permissions
 * @param userId - ID of the user
 * @returns Array of organization IDs the user can manage
 */
export function getManageableOrganizations(
  userRoles: UserRoleData[],
  userId?: string
): string[] {
  const manageableOrgIds: string[] = [];

  for (const role of userRoles) {
    if (!role.organization_id) continue;

    if (canManageOrganization(userRoles, role.organization_id, userId)) {
      manageableOrgIds.push(role.organization_id);
    }
  }

  return [...new Set(manageableOrgIds)]; // Remove duplicates
}
