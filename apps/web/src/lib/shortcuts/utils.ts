import { Modifier<PERSON><PERSON> } from './types';

const isMac = typeof window !== 'undefined' && navigator.platform.toUpperCase().indexOf('MAC') >= 0;

const modifierSymbols: Record<ModifierKey, string> = {
  ctrl: isMac ? '⌃' : 'Ctrl',
  alt: isMac ? '⌥' : 'Alt',
  shift: isMac ? '⇧' : 'Shift',
  meta: isMac ? '⌘' : 'Win'
};

export function formatShortcut(key: string, modifiers: ModifierKey[]): string {
  const sortedModifiers = [...modifiers].sort();
  const formattedModifiers = sortedModifiers.map(mod => modifierSymbols[mod]);
  return [...formattedModifiers, key.toUpperCase()].join(isMac ? '' : '+');
}

export function createActionId(category: string, action: string): string {
  return `${category}.${action}`.toLowerCase();
} 