import { KeyBinding, ShortcutManager } from './types';

class ShortcutManagerImpl implements ShortcutManager {
  private actions: Record<string, KeyBinding> = {};
  private bindings: Record<string, KeyBinding> = {};

  constructor() {
    this.loadFromStorage();
  }

  private loadFromStorage() {
    try {
      const savedBindings = localStorage.getItem('shortcutBindings');
      if (savedBindings) {
        this.bindings = JSON.parse(savedBindings);
      }
    } catch (error) {
      console.error('Failed to load shortcuts from storage:', error);
    }
  }

  private saveToStorage() {
    try {
      localStorage.setItem('shortcutBindings', JSON.stringify(this.bindings));
    } catch (error) {
      console.error('Failed to save shortcuts to storage:', error);
    }
  }

  getActions(): Record<string, KeyBinding> {
    return { ...this.actions };
  }

  getBindings(): Record<string, KeyBinding> {
    return { ...this.bindings };
  }

  setBinding(id: string, binding: KeyBinding): void {
    this.bindings[id] = binding;
    this.saveToStorage();
  }

  removeBinding(id: string): void {
    delete this.bindings[id];
    this.saveToStorage();
  }

  checkConflicts(binding: KeyBinding): string[] {
    const conflicts: string[] = [];
    const bindingKey = this.normalizeBinding(binding);

    Object.entries(this.bindings).forEach(([id, existingBinding]) => {
      if (this.normalizeBinding(existingBinding) === bindingKey) {
        conflicts.push(id);
      }
    });

    return conflicts;
  }

  private normalizeBinding(binding: KeyBinding): string {
    const sortedModifiers = [...binding.modifiers].sort();
    return `${sortedModifiers.join('+')}+${binding.key.toLowerCase()}`;
  }
}

export const shortcutManager = new ShortcutManagerImpl(); 