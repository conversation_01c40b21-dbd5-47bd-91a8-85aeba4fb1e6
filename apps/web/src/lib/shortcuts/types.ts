export type ModifierKey = "ctrl" | "alt" | "shift" | "meta";

export interface ShortcutCombo {
  key: string;
  modifiers: Modifier<PERSON>ey[];
  description: string;
}

export interface KeyBinding extends ShortcutCombo {
  action: string;
}

export interface ShortcutManager {
  getActions(): Record<string, KeyBinding>;
  getBindings(): Record<string, KeyBinding>;
  setBinding(id: string, binding: KeyBinding): void;
  removeBinding(id: string): void;
  checkConflicts(binding: KeyBinding): string[];
}

export interface ShortcutHook {
  getActions: () => Record<string, KeyBinding>;
  getBindings: () => Record<string, KeyBinding>;
  setBinding: (id: string, binding: KeyBinding) => void;
  removeBinding: (id: string) => void;
  checkConflicts: (binding: KeyBinding) => string[];
  registerShortcut: (id: string, binding: KeyBinding) => void;
  unregisterShortcut: (id: string) => void;
} 