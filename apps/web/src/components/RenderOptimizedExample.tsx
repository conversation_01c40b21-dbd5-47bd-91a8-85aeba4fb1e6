import { useState } from 'react';
import { useRenderOptimizer } from '@/hooks/useRenderOptimizer';

interface ExampleProps {
  name: string;
  count: number;
  onIncrement: () => void;
}

/**
 * Example component showing how to use the render optimizer
 */
export function RenderOptimizedExample({ name, count, onIncrement }: ExampleProps) {
  // Initialize with component name and tracked props
  useRenderOptimizer('RenderOptimizedExample', { name, count });
  
  // Local state to demonstrate render tracking
  const [localCount, setLocalCount] = useState(0);
  
  return (
    <div className="p-4 border rounded">
      <h3 className="font-medium">Render-Optimized Component</h3>
      <p>Name: {name}</p>
      <p>Prop Count: {count}</p>
      <p>Local Count: {localCount}</p>
      <div className="mt-2 space-x-2">
        <button 
          className="px-3 py-1 bg-blue-500 text-white rounded"
          onClick={onIncrement}
        >
          Increment Prop
        </button>
        <button 
          className="px-3 py-1 bg-green-500 text-white rounded"
          onClick={() => setLocalCount(c => c + 1)}
        >
          Increment Local
        </button>
      </div>
      <p className="mt-4 text-sm text-gray-500">
        Check the console to see render tracking
      </p>
    </div>
  );
} 