import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/hooks/useAuth";
import { supabase } from "@/lib/supabase";
import { Loader2 } from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";

// Simple loading spinner component
function LoadingSpinner({ size = "sm" }: { size?: "sm" | "lg" }) {
  return (
    <Loader2 className={`animate-spin ${size === "lg" ? "h-10 w-10" : "h-4 w-4"}`} />
  );
}

export function OrganizationSelector() {
  const [orgName, setOrgName] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const [autoCreating, setAutoCreating] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('Setting up your practice...');
  // Flag to prevent multiple simultaneous organization creations
  const [creationInProgress, setCreationInProgress] = useState(false);

  const { user, organization, setOrganization } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();

  // If user already has an organization in context, redirect to dashboard
  useEffect(() => {
    if (organization) {
      navigate('/dashboard', { replace: true });
    }
  }, [organization, navigate]);

  // Define createNewOrganization function with useCallback to avoid dependency issues
  const createNewOrganization = useCallback(async (name?: string) => {
    if (!user || creationInProgress) return;

    // Create a unique key for this organization creation to avoid duplicates
    const creationKey = `spritely_org_creation_${user.id}`;

    // Check if we're already in the process of creating an org for this user
    try {
      const existingCreationProcess = localStorage.getItem(creationKey);
      if (existingCreationProcess) {
        const parsedProcess = JSON.parse(existingCreationProcess);
        const creationTime = new Date(parsedProcess.timestamp).getTime();
        const now = Date.now();

        // If another creation process started less than 10 seconds ago, abort this one
        if (now - creationTime < 10000) {
          if (process.env.NODE_ENV === 'development') {
            console.info('Skipping duplicate organization creation - another process started recently');
          }
          return;
        }
      }

      // Set a flag that we're starting creation
      localStorage.setItem(creationKey, JSON.stringify({
        timestamp: new Date().toISOString(),
        name: name || 'auto-created'
      }));
    } catch {
      // Ignore any localStorage errors
    }

    try {
      setCreationInProgress(true);
      setAutoCreating(true);
      setLoadingMessage('Setting up your practice...');

      // First, check if user already has any organizations
      const { data: existingRoles } = await supabase
        .from('user_roles')
        .select('organization_id')
        .eq('user_id', user.id);

      if (existingRoles && existingRoles.length > 0) {
        // User already has organizations assigned, don't create another one
        if (process.env.NODE_ENV === 'development') {
          console.info('User already has organizations, skipping creation');
        }

        // Try to fetch the organization details
        const organizationId = existingRoles[0].organization_id;
        if (!organizationId) {
          console.warn('Organization ID is null');
          return;
        }

        const { data: existingOrg } = await supabase
          .from('organizations')
          .select('*')
          .eq('id', organizationId)
          .single();

        if (existingOrg) {
          if (process.env.NODE_ENV === 'development') {
            console.info('Found existing organization, using it instead of creating new');
          }

          setOrganization(existingOrg);
          setLoadingMessage('Redirecting to dashboard...');

          // Navigate to the dashboard
          setTimeout(() => {
            navigate('/dashboard');
          }, 1000);

          return;
        }
      }

      // If no name provided, use default
      const organizationName = name || (user.user_metadata?.first_name
        ? `${user.user_metadata.first_name}'s Practice`
        : `New Practice`);

      if (process.env.NODE_ENV === 'development') {
        console.info('Creating new organization:', organizationName);
      }

      // Create the organization
      const { data: newOrg, error: createError } = await supabase
        .from('organizations')
        .insert({
          name: organizationName,
          type: 'personal'
        })
        .select()
        .single();

      if (createError) {
        console.error('Error creating organization:', createError);
        setAutoCreating(false);
        toast({
          title: 'Error',
          description: 'Failed to create your practice. Please try again.',
          variant: 'error',
        });
        return;
      }

      if (process.env.NODE_ENV === 'development') {
        console.info('Created organization:', newOrg);
      }
      setLoadingMessage('Assigning you as admin...');

      // Assign user as admin
      const { error: roleError } = await supabase
        .from('user_roles')
        .insert({
          user_id: user.id,
          organization_id: newOrg.id,
          role: 'org_admin'
        });

      if (roleError) {
        console.error('Error assigning user role:', roleError);
        toast({
          title: 'Warning',
          description: 'Your practice was created but role assignment failed. Contact support if you experience issues.',
          variant: 'warning',
        });
      }

      // Update the context with the new organization
      setOrganization(newOrg);
      setLoadingMessage('Redirecting to dashboard...');

      // Navigate to the dashboard
      setTimeout(() => {
        navigate('/dashboard');
      }, 1000);

    } catch (error) {
      console.error('Error in organization creation:', error);
      setAutoCreating(false);
      toast({
        title: 'Error',
        description: 'Failed to set up your practice. Please try again.',
        variant: 'error',
      });
    } finally {
      setLoading(false);
      setCreationInProgress(false);
    }
  }, [user, navigate, setOrganization, creationInProgress, toast]);

  useEffect(() => {
    if (user?.user_metadata?.first_name) {
      setOrgName(`${user.user_metadata.first_name}'s Practice`);
    } else if (user?.email) {
      const emailName = user.email.split('@')[0];
      setOrgName(`${emailName}'s Practice`);
    } else {
      setOrgName("My Healthcare Practice");
    }
  }, [user]);

  // Use a ref to track if we've already attempted creation to avoid double creation
  const hasInitiatedCreation = useRef(false);

  // Check if the user already has an organization before attempting auto-creation
  useEffect(() => {
    if (loading || !user || hasInitiatedCreation.current) return;

    // Mark that we've initiated the process
    hasInitiatedCreation.current = true;

    const checkForExistingOrg = async () => {
      try {
        // Check if user already has an organization role before we try to create one
        const { data: existingRoles } = await supabase
          .from('user_roles')
          .select('organization_id')
          .eq('user_id', user.id);

        if (existingRoles && existingRoles.length > 0) {
          // User already has an organization, don't auto-create
          if (process.env.NODE_ENV === 'development') {
            console.info('User already has an organization, skipping auto-creation');
          }

          // Fetch the full organization details
          const organizationId = existingRoles[0].organization_id;
          if (!organizationId) {
            console.warn('Organization ID is null');
            return;
          }

          const { data: existingOrg } = await supabase
            .from('organizations')
            .select('*')
            .eq('id', organizationId)
            .single();

          if (existingOrg) {
            // Set the organization in context
            setOrganization(existingOrg);

            // Navigate to dashboard after a slight delay
            setTimeout(() => {
              navigate('/dashboard');
            }, 10);

            return;
          }
        }

        // Proceed with creation process
        createNewOrganization();

      } catch (error) {
        console.error('Error checking for existing organization:', error);
        // Fall back to creation process
        createNewOrganization();
      }
    };

    // Start the check with a slight delay
    setTimeout(checkForExistingOrg, 50);

    // Cleanup function to clear creation flag on unmount
    return () => {
      if (user) {
        try {
          const creationKey = `spritely_org_creation_${user.id}`;
          localStorage.removeItem(creationKey);
        } catch {
          // Ignore localStorage errors
        }
      }
    };
  }, [user, loading, navigate, setOrganization, createNewOrganization]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user || !orgName.trim()) return;

    setLoading(true);

    try {
      await createNewOrganization(orgName.trim());
    } finally {
      setLoading(false);
    }
  };

  if (autoCreating) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Setting Up Your Account</CardTitle>
            <CardDescription>
              We're creating your healthcare practice.
            </CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <LoadingSpinner size="lg" />
            <p className="mt-4 text-center text-muted-foreground">{loadingMessage}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-background p-4">
      <Card className="w-full max-w-md">
        <form onSubmit={handleSubmit}>
          <CardHeader>
            <CardTitle>Setting Up Your Account</CardTitle>
            <CardDescription>
              We're creating your healthcare practice.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid w-full items-center gap-4">
              <div className="flex flex-col space-y-1.5">
                <label htmlFor="name">Practice Name</label>
                <Input
                  id="name"
                  placeholder="Enter practice name"
                  value={orgName}
                  onChange={(e) => setOrgName(e.target.value)}
                  disabled={loading}
                  required
                />
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" onClick={() => navigate('/login')} disabled={loading}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading || !orgName.trim()}>
              {loading ? <LoadingSpinner size="sm" /> : 'Create Practice'}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}