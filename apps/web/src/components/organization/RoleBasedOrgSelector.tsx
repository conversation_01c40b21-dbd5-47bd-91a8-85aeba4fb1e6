import { Button } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Organization } from "@/contexts/auth-context-types";
import { useAuth } from "@/hooks/useAuth";
import { useUserRoles } from "@/hooks/useUserRoles";
import { EnhancedOrganization } from "@/lib/auth/organization-types";
import { supabase } from "@/lib/supabase";
import { Building2, ChevronDown, Edit, Search } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

export function RoleBasedOrgSelector() {
  const { user, organization, setOrganization } = useAuth();
  const { isSystemAdmin, isAdmin } = useUserRoles();
  const navigate = useNavigate();
  const searchInputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [filteredOrganizations, setFilteredOrganizations] = useState<Organization[]>([]);
  const [hasMultipleOrgs, setHasMultipleOrgs] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [highlightedIndex, setHighlightedIndex] = useState(-1);

  // Fetch all organizations for system admins
  useEffect(() => {
    const fetchOrganizations = async () => {
      if (!user || !isSystemAdmin) return;

      try {
        setIsLoading(true);
        const { data, error } = await supabase
          .from('organizations')
          .select('*')
          .order('name');

        if (error) {
          console.error('Error fetching all organizations:', error);
          return;
        }

        if (data && data.length > 0) {
          setOrganizations(data);
          setFilteredOrganizations(data);
          setHasMultipleOrgs(data.length > 1);

          console.debug(`Loaded ${data.length} organizations for system admin`);
        }
      } catch (err) {
        console.error('Error fetching all organizations:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrganizations();
  }, [user, isSystemAdmin]);

  // Get organizations from the current organization if it's enhanced
  useEffect(() => {
    if (organization && !isSystemAdmin) {
      const enhancedOrg = organization as EnhancedOrganization;
      const hasMultiple = !!enhancedOrg.hasMultipleOrgs;
      setHasMultipleOrgs(hasMultiple);

      if (enhancedOrg.availableOrgs && enhancedOrg.availableOrgs.length > 0) {
        setOrganizations(enhancedOrg.availableOrgs);
        setFilteredOrganizations(enhancedOrg.availableOrgs);
        console.debug(`Loaded ${enhancedOrg.availableOrgs.length} organizations for user`);
      }
    }
  }, [organization, isSystemAdmin]);

  // Filter organizations based on search term
  useEffect(() => {
    if (!searchTerm) {
      setFilteredOrganizations(organizations);
    } else {
      const filtered = organizations.filter(org =>
        org.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        org.type?.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredOrganizations(filtered);
    }

    // Reset highlighted index when filtered results change
    setHighlightedIndex(-1);

    // Maintain focus on search input after filtering
    if (searchInputRef.current && searchTerm) {
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 0);
    }
  }, [searchTerm, organizations]);

  // Keyboard navigation handler
  const handleKeyDown = (e: React.KeyboardEvent) => {
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setHighlightedIndex(prev =>
          prev < filteredOrganizations.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setHighlightedIndex(prev =>
          prev > 0 ? prev - 1 : filteredOrganizations.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (highlightedIndex >= 0 && highlightedIndex < filteredOrganizations.length) {
          const selectedOrg = filteredOrganizations[highlightedIndex];
          handleOrganizationSelect(selectedOrg);
        }
        break;
      case 'Escape':
        e.preventDefault();
        // Close dropdown logic would go here
        break;
    }
  };

  // Scroll highlighted item into view
  useEffect(() => {
    if (highlightedIndex >= 0 && dropdownRef.current) {
      const highlightedElement = dropdownRef.current.querySelector(
        `[data-index="${highlightedIndex}"]`
      ) as HTMLElement;
      if (highlightedElement) {
        highlightedElement.scrollIntoView({
          block: 'nearest',
          behavior: 'smooth'
        });
      }
    }
  }, [highlightedIndex]);

  const handleOrganizationSelect = async (org: Organization) => {
    if (!user || !org) return;

    try {
      setIsLoading(true);
      setSearchTerm(""); // Clear search

      // Set the organization in the auth context (this handles caching automatically)
      setOrganization(org);

      // Show success message and redirect
      setTimeout(() => {
        toast.success(`Switched to ${org.name}`);

        // Refresh the page to ensure all components update with the new organization
        window.location.href = '/dashboard';
      }, 300);
    } catch (err: unknown) {
      const error = err as { message?: string };
      console.error('Failed to switch organization:', err);
      toast.error(`Failed to switch organization: ${error.message || 'Unknown error'}`);
      setIsLoading(false);
    }
  };

  // Handle edit organization click
  const handleEditOrganization = (e: React.MouseEvent, orgId: string) => {
    e.preventDefault();
    e.stopPropagation();
    navigate(`/organizations/${orgId}/settings`);
  };

  // Handle search input changes
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.stopPropagation();
    setSearchTerm(e.target.value);
  };

  // Handle search input events
  const handleSearchInputEvents = (e: React.KeyboardEvent<HTMLInputElement> | React.MouseEvent<HTMLInputElement>) => {
    e.stopPropagation();
  };

  // Always show the current organization, but only make it a dropdown if:
  // 1. User is an admin (system_admin or org_admin)
  // 2. User is a system admin (who can see all orgs) or has multiple organizations
  const canSwitchOrgs = isAdmin && (isSystemAdmin || hasMultipleOrgs);

  // If there's no organization at all, show a placeholder
  if (!organization && organizations.length === 0) {
    return (
      <Button variant="ghost" size="sm" className="flex items-center gap-2" disabled>
        <Building2 className="h-4 w-4" />
        <span className="max-w-[200px] truncate">No Organization</span>
      </Button>
    );
  }

  // If user can't switch orgs, just show the current org without dropdown
  if (!canSwitchOrgs) {
    return (
      <Button variant="ghost" size="sm" className="flex items-center gap-2" disabled={isLoading}>
        <Building2 className="h-4 w-4" />
        <span className="max-w-[200px] truncate font-medium">
          {organization?.name || "Select Organization"}
        </span>
      </Button>
    );
  }

  // Otherwise, show the dropdown with all available organizations
  return (
    <DropdownMenu onOpenChange={(open) => {
      if (!open) {
        // Delay clearing search term to prevent flash during close animation
        setTimeout(() => {
          setSearchTerm("");
          setHighlightedIndex(-1);
        }, 150);
      } else {
        // Focus the search input when dropdown opens
        setTimeout(() => {
          searchInputRef.current?.focus();
        }, 100);
      }
    }}>
      <DropdownMenuTrigger asChild disabled={isLoading}>
        <Button variant="ghost" size="sm" className="flex items-center gap-2 min-w-[200px]">
          <Building2 className="h-5 w-5 text-primary" />
          <span className="max-w-[150px] truncate font-medium">
            {organization?.name || "Select Organization"}
          </span>
          <ChevronDown className="h-4 w-4 opacity-50 ml-auto" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="center" className="w-[320px]" onKeyDown={handleKeyDown}>
        <DropdownMenuLabel>Switch Organization</DropdownMenuLabel>
        <DropdownMenuSeparator />

        {/* Search Input - only show if there are multiple organizations */}
        {organizations.length > 3 && (
          <>
            <div className="px-2 py-1">
              <div className="relative">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground pointer-events-none" />
                <Input
                  ref={searchInputRef}
                  placeholder="Search organizations..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  onKeyDown={handleKeyDown}
                  onMouseDown={handleSearchInputEvents}
                  onClick={handleSearchInputEvents}
                  className="pl-8 h-8"
                  autoComplete="off"
                />
              </div>
            </div>
            <DropdownMenuSeparator />
          </>
        )}

        {/* Organizations List */}
        <div ref={dropdownRef} className="max-h-[300px] overflow-y-auto">
          {filteredOrganizations.map((org: Organization, index: number) => {
            const isCurrent = organization && organization.id === org.id;
            return (
              <DropdownMenuItem
                key={org.id}
                disabled={isLoading || isCurrent}
                className={`flex items-center justify-between ${
                  highlightedIndex === index ? 'bg-muted' : ''
                }`}
                onSelect={(e) => {
                  e.preventDefault();
                  handleOrganizationSelect(org);
                }}
                onMouseDown={(e) => {
                  // Only trigger if it's not the edit button
                  if ((e.target as HTMLElement).closest('button[data-edit]')) {
                    return;
                  }
                  handleOrganizationSelect(org);
                }}
                onMouseEnter={() => setHighlightedIndex(index)}
                data-index={index}
              >
                <div className="flex items-center flex-1">
                  <span className="truncate">{org.name}</span>
                  {isCurrent && (
                    <span className="ml-2 text-xs text-primary font-medium">(Current)</span>
                  )}
                </div>
                {isSystemAdmin && (
                  <Button
                    data-edit="true"
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 ml-2 opacity-60 hover:opacity-100"
                    onMouseDown={(e) => handleEditOrganization(e, org.id)}
                    onClick={(e) => handleEditOrganization(e, org.id)}
                  >
                    <Edit className="h-3 w-3" />
                  </Button>
                )}
              </DropdownMenuItem>
            );
          })}

          {filteredOrganizations.length === 0 && searchTerm && (
            <div className="px-2 py-4 text-center text-sm text-muted-foreground">
              No organizations found matching "{searchTerm}"
            </div>
          )}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
