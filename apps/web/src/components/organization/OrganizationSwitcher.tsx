import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { Organization as ContextOrganization } from "@/contexts/auth-context-types";
import { useAuth } from "@/hooks/useAuth";
import { canManageOrganization, isSystemAdmin } from "@/lib/permissions/organization-permissions";
import { supabase } from "@/lib/supabase";
import { Building2, ChevronDown, Edit, Search, Settings, Table } from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

// Define simplified types for what we actually need
interface Organization {
  id: string;
  name: string;
  type: string;
  settings: Record<string, unknown>;
}

interface UserRoleWithOrganization {
  role: string;
  invitation_status: string | null;
  custom_permissions: Record<string, unknown>;
  organization: Organization;
}


export function OrganizationSwitcher() {
  const { user, organization: currentOrg, setOrganization } = useAuth();
  const navigate = useNavigate();
  const searchInputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [userRoles, setUserRoles] = useState<UserRoleWithOrganization[]>([]);
  const [filteredRoles, setFilteredRoles] = useState<UserRoleWithOrganization[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const [isOpen, setIsOpen] = useState(false);

  const fetchUserRoles = useCallback(async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      const { data, error } = await supabase
        .from('user_roles')
        .select(`
          role,
          invitation_status,
          custom_permissions,
          organization:organizations (
            id,
            name,
            type,
            settings,
            owner_id
          )
        `)
        .eq('user_id', user.id);

      if (error) throw error;

      // Cast the data to handle the structure mismatch
      const rawData = data as unknown as Array<{
        role: string;
        invitation_status: string | null;
        custom_permissions: Record<string, unknown>;
        organization: {
          id: string;
          name: string;
          type: string;
          settings: Record<string, unknown>;
          owner_id: string;
        }
      }>;

      // Transform the data to match our expected structure
      const transformedData: UserRoleWithOrganization[] = rawData.map(item => ({
        role: item.role,
        invitation_status: item.invitation_status,
        custom_permissions: item.custom_permissions || {},
        organization: {
          id: item.organization.id,
          name: item.organization.name,
          type: item.organization.type,
          settings: item.organization.settings || {}
        }
      }));

      setUserRoles(transformedData);
      setFilteredRoles(transformedData);
    } catch (error) {
      console.error('Error fetching user roles:', error);
      toast.error('Failed to load organizations');
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  useEffect(() => {
    fetchUserRoles();
  }, [fetchUserRoles]);

  // Filter organizations based on search term
  useEffect(() => {
    if (!searchTerm) {
      setFilteredRoles(userRoles);
    } else {
      const filtered = userRoles.filter(role =>
        role.organization.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        role.organization.type.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredRoles(filtered);
    }
    
    // Reset highlighted index when filtered results change
    setHighlightedIndex(-1);
    
    // Maintain focus on search input after filtering
    if (searchInputRef.current && searchTerm) {
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 0);
    }
  }, [searchTerm, userRoles]);

  // Keyboard navigation handler
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setHighlightedIndex(prev => 
          prev < filteredRoles.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setHighlightedIndex(prev => 
          prev > 0 ? prev - 1 : filteredRoles.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (highlightedIndex >= 0 && highlightedIndex < filteredRoles.length) {
          const selectedRole = filteredRoles[highlightedIndex];
          handleOrganizationSelect(selectedRole.organization);
        }
        break;
      case 'Escape':
        e.preventDefault();
        setIsOpen(false);
        break;
    }
  };

  // Scroll highlighted item into view
  useEffect(() => {
    if (highlightedIndex >= 0 && dropdownRef.current) {
      const highlightedElement = dropdownRef.current.querySelector(
        `[data-index="${highlightedIndex}"]`
      ) as HTMLElement;
      if (highlightedElement) {
        highlightedElement.scrollIntoView({
          block: 'nearest',
          behavior: 'smooth'
        });
      }
    }
  }, [highlightedIndex]);

  const handleOrganizationSelect = (org: Organization) => {
    try {
      // Clear search term
      setSearchTerm("");

      // Convert our simplified Organization to the full type expected by setOrganization
      const fullOrg = {
        id: org.id,
        name: org.name,
        type: org.type,
        settings: org.settings,
        // Add missing properties with default values
        billing_info: {},
        created_at: new Date().toISOString(),
        subscription_tier: 'free',
        updated_at: new Date().toISOString()
      };

      // Cast to the expected type
      setOrganization(fullOrg as unknown as ContextOrganization);
      toast.success(`Switched to ${org.name}`);
      navigate('/dashboard');
    } catch (error) {
      console.error('Error switching organization:', error);
      toast.error('Failed to switch organization');
    }
  };

  if (isLoading) {
    return (
      <Skeleton className="h-9 w-[200px]" />
    );
  }

  if (!currentOrg || userRoles.length === 0) {
    return (
      <Button
        variant="outline"
        size="sm"
        className="gap-2"
        onClick={() => navigate('/setup')}
      >
        <Building2 className="h-4 w-4" />
        <span>Select Organization</span>
      </Button>
    );
  }

  const isAdmin = userRoles.some(
    role => role.organization.id === currentOrg?.id &&
    ['system_admin', 'org_admin'].includes(role.role)
  );

  // Function to check if user can edit a specific organization
  const canEditOrganization = (orgId: string) => {
    const roleData = userRoles.map(role => ({
      role: role.role,
      custom_permissions: role.custom_permissions,
      organization_id: role.organization.id
    }));

    return canManageOrganization(roleData, orgId, user?.id);
  };

  // Check if user is a system admin (can manage all organizations)
  const userIsSystemAdmin = isSystemAdmin(userRoles.map(role => ({ role: role.role })));

  // Handle edit organization click
  const handleEditOrganization = (e: React.MouseEvent, orgId: string) => {
    e.preventDefault();
    e.stopPropagation();
    navigate(`/organizations/${orgId}/settings`);
  };

  // Handle search input changes
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.stopPropagation();
    setSearchTerm(e.target.value);
  };

  // Handle search input events
  const handleSearchInputEvents = (e: React.KeyboardEvent<HTMLInputElement> | React.MouseEvent<HTMLInputElement>) => {
    e.stopPropagation();
  };

  return (
    <DropdownMenu onOpenChange={(open) => {
      setIsOpen(open);
      if (!open) {
        // Delay clearing search term to prevent flash during close animation
        setTimeout(() => {
          setSearchTerm("");
          setHighlightedIndex(-1);
        }, 150);
      } else {
        // Focus the search input when dropdown opens
        setTimeout(() => {
          searchInputRef.current?.focus();
        }, 100);
      }
    }}>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <Building2 className="h-4 w-4" />
          <span className="max-w-[150px] truncate">{currentOrg.name}</span>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[320px]" onKeyDown={handleKeyDown}>
        <DropdownMenuLabel>Manage Organizations</DropdownMenuLabel>
        <DropdownMenuSeparator />

        {/* Search Input */}
        <div className="px-2 py-1">
          <div className="relative">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground pointer-events-none" />
            <Input
              ref={searchInputRef}
              placeholder="Search organizations..."
              value={searchTerm}
              onChange={handleSearchChange}
              onKeyDown={handleKeyDown}
              onMouseDown={handleSearchInputEvents}
              onClick={handleSearchInputEvents}
              className="pl-8 h-8"
              autoComplete="off"
            />
          </div>
        </div>
        <DropdownMenuSeparator />

        {/* Organizations List */}
        <div ref={dropdownRef} className="max-h-[300px] overflow-y-auto">
          {filteredRoles.map(({ organization: org }, index) => (
          <DropdownMenuItem
            key={org.id}
            className={`cursor-pointer flex items-center justify-between ${highlightedIndex === index ? 'bg-muted' : ''}`}
            onSelect={(e) => {
              e.preventDefault();
              handleOrganizationSelect(org);
            }}
            onMouseDown={(e) => {
              // Only trigger if it's not the edit button
              if ((e.target as HTMLElement).closest('button[data-edit]')) {
                return;
              }
              handleOrganizationSelect(org);
            }}
            onMouseEnter={() => setHighlightedIndex(index)}
            data-index={index}
          >
            <div className="flex items-center flex-1">
              <span>{org.name}</span>
              {currentOrg.id === org.id && (
                <span className="ml-2 text-xs text-muted-foreground">(Current)</span>
              )}
            </div>
            {canEditOrganization(org.id) && (
              <Button
                data-edit="true"
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 ml-2 opacity-60 hover:opacity-100"
                onMouseDown={(e) => handleEditOrganization(e, org.id)}
                onClick={(e) => handleEditOrganization(e, org.id)}
              >
                <Edit className="h-3 w-3" />
              </Button>
            )}
          </DropdownMenuItem>
        ))}

        {filteredRoles.length === 0 && searchTerm && (
          <div className="px-2 py-4 text-center text-sm text-muted-foreground">
            No organizations found matching "{searchTerm}"
          </div>
        )}
        </div>

        <DropdownMenuSeparator />

        {/* Management Options */}
        {userIsSystemAdmin && (
          <DropdownMenuItem
            className="cursor-pointer"
            onSelect={() => navigate('/organizations/manage')}
          >
            <Table className="mr-2 h-4 w-4" />
            View All Organizations
          </DropdownMenuItem>
        )}
        {isAdmin && (
          <DropdownMenuItem
            className="cursor-pointer"
            onSelect={() => navigate(`/organizations/${currentOrg.id}/settings`)}
          >
            <Settings className="mr-2 h-4 w-4" />
            Organization Settings
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}