import { useAuth } from '@/hooks/useAuth';

interface LoadingScreenProps {
  isPostLoginLoading: boolean;
  showRecoveryButton: boolean;
  handleBackToLogin: () => void;
  handleReloadApplication: () => void;
}

export function LoadingScreen({ 
  isPostLoginLoading, 
  showRecoveryButton, 
  handleBackToLogin,
  handleReloadApplication
}: LoadingScreenProps) {
  const { user, isLoading, organization } = useAuth();
  
  // Display appropriate loading message based on current state
  const loadingMessage = (() => {
    if (isLoading) return "Connecting to your account...";
    if (user && !organization && isPostLoginLoading) return "Setting up your dashboard...";
    if (user && organization) return "Finalizing your practice...";
    return "Loading your practice...";
  })();
  
  return (
    <div className="flex h-screen items-center justify-center">
      <div className="text-center">
        <h2 className="text-2xl font-semibold mb-2">Almost there!</h2>
        <p className="text-gray-500 mb-6">{loadingMessage}</p>
        
        {/* Show recovery button after timeout */}
        {showRecoveryButton && (
          <div className="mt-8">
            <p className="text-sm text-gray-500 mb-2">
              This is taking longer than usual
            </p>
            <div className="flex space-x-4 justify-center mt-2">
              <button
                onClick={handleReloadApplication}
                className="text-blue-500 hover:text-blue-700 underline text-sm font-medium"
              >
                Retry
              </button>
              <button
                onClick={handleBackToLogin}
                className="text-blue-500 hover:text-blue-700 underline text-sm font-medium"
              >
                Back to Login
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 