import { useAuth } from '@/hooks/useAuth';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { LoadingScreen } from './LoadingScreen';

export function LoadingStateManager({ children }: { children: React.ReactNode }) {
  const { user, organization, isLoading } = useAuth();

  // Track if we need to show the recovery button after a timeout
  const [showRecoveryButton, setShowRecoveryButton] = useState(false);

  // Track if we should show the manual recovery UI after a longer timeout
  const [showManualRecoveryUI, setShowManualRecoveryUI] = useState(false);

  // Track loading UI state with refs to avoid extra renders
  const recoveryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const manualRecoveryTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Count mount/update cycles to detect excessive updates
  const renderCountRef = useRef(0);

  // Track when loading started
  const loadingStartTimeRef = useRef(Date.now());

  // Record the last seen state to detect potential loops
  const lastSeenStateRef = useRef({
    isLoading,
    userId: user?.id,
    hasOrg: !!organization
  });

  // Clean loading state logic that minimizes re-renders
  const shouldShowLoading = useMemo(() => {
    // Only show loading screen during initial load or when explicitly loading
    return isLoading;
  }, [isLoading]);

  // Detect and log potential state loops
  useEffect(() => {
    renderCountRef.current += 1;

    // Track state changes
    const currentState = {
      isLoading,
      userId: user?.id,
      hasOrg: !!organization
    };

    // Log excessive renders in development
    if (process.env.NODE_ENV === 'development' && renderCountRef.current > 20) {
      console.warn(
        `LoadingStateManager rendered ${renderCountRef.current} times. ` +
        `Current state: ${JSON.stringify(currentState)}`
      );
    }

    lastSeenStateRef.current = currentState;
  }, [isLoading, user?.id, organization]);

  // Handler for manually reloading the app
  const handleReloadApplication = useCallback(() => {
    if (process.env.NODE_ENV === 'development') {
      console.info('Manual reload triggered');
    }

    // Force a hard reload to clear any React state issues
    window.location.reload();
  }, []);

  // Handler for manually going back to login if stuck
  const handleBackToLogin = useCallback(() => {
    // Clear UI state
    setShowRecoveryButton(false);
    setShowManualRecoveryUI(false);

    // Clear any pending timeouts
    if (recoveryTimeoutRef.current) {
      clearTimeout(recoveryTimeoutRef.current);
      recoveryTimeoutRef.current = null;
    }

    if (manualRecoveryTimeoutRef.current) {
      clearTimeout(manualRecoveryTimeoutRef.current);
      manualRecoveryTimeoutRef.current = null;
    }

    // Log the action in development
    if (process.env.NODE_ENV === 'development') {
      console.info('Manual recovery triggered - returning to login');
    }

    // Navigate to login by replacing location (avoid React Router for simplicity)
    window.location.href = '/login';

    // Try to clear any cached state that might be causing issues
    try {
      localStorage.removeItem('spritely_org_cache');
      sessionStorage.clear();
    } catch (error) {
      console.warn('Error clearing cache during recovery:', error);
    }
  }, []);

  // Set up a recovery button if loading takes too long
  useEffect(() => {
    // Reset loading start time when loading state changes
    if (shouldShowLoading) {
      loadingStartTimeRef.current = Date.now();
    }

    // Only set up recovery timeout during loading
    if (shouldShowLoading) {
      // Show recovery button after 3 seconds of loading
      recoveryTimeoutRef.current = setTimeout(() => {
        setShowRecoveryButton(true);

        if (process.env.NODE_ENV === 'development') {
          console.info('Showing recovery button - loading took too long');
        }

        // Show manual recovery UI after 5 more seconds (8 seconds total)
        manualRecoveryTimeoutRef.current = setTimeout(() => {
          setShowManualRecoveryUI(true);

          if (process.env.NODE_ENV === 'development') {
            console.info('Showing manual recovery UI - loading appears stuck');
          }
        }, 5000);
      }, 3000);
    } else {
      // Clear recovery UI when not loading
      setShowRecoveryButton(false);
      setShowManualRecoveryUI(false);
    }

    // Cleanup function to clear timeouts on unmount or deps change
    return () => {
      if (recoveryTimeoutRef.current) {
        clearTimeout(recoveryTimeoutRef.current);
        recoveryTimeoutRef.current = null;
      }

      if (manualRecoveryTimeoutRef.current) {
        clearTimeout(manualRecoveryTimeoutRef.current);
        manualRecoveryTimeoutRef.current = null;
      }
    };
  }, [shouldShowLoading]);

  // Extract complex dependency expressions to separate variables
  const userId = user?.id || null;
  const hasOrganization = !!organization;

  // Debug logging - only when state changes meaningfully
  useEffect(() => {
    // Disable logging to prevent excessive console entries
    // if (process.env.NODE_ENV === 'development') {
    //   // Only log when values change meaningfully
    //   const logState = {
    //     isLoggedIn: !!user,
    //     hasOrganization,
    //     userId,
    //     isLoading,
    //     showingRecoveryButton: showRecoveryButton,
    //     showingManualRecoveryUI: showManualRecoveryUI,
    //     loadingTime: shouldShowLoading ? `${Date.now() - loadingStartTimeRef.current}ms` : 'not loading'
    //   };

    //   console.info('Loading state:', logState);
    // }
  }, [userId, hasOrganization, isLoading, showRecoveryButton, showManualRecoveryUI, shouldShowLoading, user]);

  // If we're showing the manual recovery UI, render it immediately
  if (showManualRecoveryUI) {
    return (
      <div className="fixed inset-0 bg-white flex flex-col items-center justify-center p-6 z-50">
        <h2 className="text-xl font-bold mb-4">Loading Taking Too Long</h2>
        <p className="text-gray-600 mb-8 text-center max-w-md">
          Something seems to be taking longer than expected. You can try reloading the application.
        </p>
        <div className="flex flex-col gap-4 w-full max-w-xs">
          <button
            onClick={handleReloadApplication}
            className="w-full py-2 px-4 bg-blue-600 text-white rounded hover:bg-blue-700 transition"
          >
            Reload Application
          </button>
          <button
            onClick={handleBackToLogin}
            className="w-full py-2 px-4 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition"
          >
            Return to Login
          </button>
        </div>
      </div>
    );
  }

  // Render loading screen or children based on loading state
  if (shouldShowLoading) {
    return (
      <LoadingScreen
        isPostLoginLoading={!!user}
        showRecoveryButton={showRecoveryButton}
        handleBackToLogin={handleBackToLogin}
        handleReloadApplication={handleReloadApplication}
      />
    );
  }

  // Not loading, render children
  return <>{children}</>;
}