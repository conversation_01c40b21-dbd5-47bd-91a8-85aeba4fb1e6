import { Organization } from '@/contexts/auth-context-types';

/**
 * Organization selector configuration options
 */
export interface OrganizationSelectorConfig {
  /** Whether to show search input */
  showSearch?: boolean;
  /** Minimum number of organizations to show search */
  searchThreshold?: number;
  /** Maximum height of the dropdown */
  maxHeight?: number;
  /** Whether to enable virtualization for large lists */
  enableVirtualization?: boolean;
  /** Number of items to render in virtual list */
  virtualItemCount?: number;
  /** Whether to show organization actions (edit, manage) */
  showActions?: boolean;
  /** Whether to allow multi-selection */
  multiSelect?: boolean;
  /** Custom placeholder text */
  placeholder?: string;
  /** Custom search placeholder */
  searchPlaceholder?: string;
  /** Whether to show current organization indicator */
  showCurrentIndicator?: boolean;
  /** Whether to disable current organization selection */
  disableCurrentSelection?: boolean;
}

/**
 * Organization item with additional metadata
 */
export interface OrganizationItem extends Organization {
  /** Whether this is the currently selected organization */
  isCurrent?: boolean;
  /** Whether the user can edit this organization */
  canEdit?: boolean;
  /** Whether the user can manage this organization */
  canManage?: boolean;
  /** Whether this item is disabled */
  disabled?: boolean;
  /** Additional metadata */
  metadata?: Record<string, any>;
}

/**
 * Organization selector state
 */
export interface OrganizationSelectorState {
  /** Whether the dropdown is open */
  isOpen: boolean;
  /** Whether data is loading */
  isLoading: boolean;
  /** Current search term */
  searchTerm: string;
  /** Currently highlighted item index */
  highlightedIndex: number;
  /** Available organizations */
  organizations: OrganizationItem[];
  /** Filtered organizations based on search */
  filteredOrganizations: OrganizationItem[];
  /** Selected organization(s) */
  selectedOrganizations: OrganizationItem[];
  /** Error state */
  error: string | null;
}

/**
 * Keyboard navigation state
 */
export interface KeyboardNavigationState {
  /** Currently focused item index */
  focusedIndex: number;
  /** Whether keyboard navigation is active */
  isActive: boolean;
  /** Last navigation direction */
  lastDirection: 'up' | 'down' | null;
}

/**
 * Organization selector callbacks
 */
export interface OrganizationSelectorCallbacks {
  /** Called when an organization is selected */
  onSelect?: (organization: OrganizationItem) => void;
  /** Called when multiple organizations are selected */
  onMultiSelect?: (organizations: OrganizationItem[]) => void;
  /** Called when search term changes */
  onSearchChange?: (searchTerm: string) => void;
  /** Called when dropdown opens/closes */
  onOpenChange?: (isOpen: boolean) => void;
  /** Called when edit action is triggered */
  onEdit?: (organization: OrganizationItem) => void;
  /** Called when manage action is triggered */
  onManage?: (organization: OrganizationItem) => void;
  /** Called when create new organization is triggered */
  onCreate?: () => void;
  /** Called when an error occurs */
  onError?: (error: string) => void;
}

/**
 * Organization selector props
 */
export interface OrganizationSelectorProps extends OrganizationSelectorCallbacks {
  /** Configuration options */
  config?: OrganizationSelectorConfig;
  /** Current organization */
  currentOrganization?: Organization | null;
  /** Available organizations */
  organizations?: Organization[];
  /** Whether the component is disabled */
  disabled?: boolean;
  /** Custom CSS class */
  className?: string;
  /** Custom trigger content */
  trigger?: React.ReactNode;
  /** Whether to show loading state */
  loading?: boolean;
}

/**
 * Search and filter options
 */
export interface SearchOptions {
  /** Fields to search in */
  searchFields?: (keyof Organization)[];
  /** Whether search is case sensitive */
  caseSensitive?: boolean;
  /** Whether to use fuzzy search */
  fuzzySearch?: boolean;
  /** Custom search function */
  customSearch?: (organizations: OrganizationItem[], searchTerm: string) => OrganizationItem[];
}

/**
 * Sort options
 */
export interface SortOptions {
  /** Field to sort by */
  sortBy?: keyof Organization;
  /** Sort direction */
  sortDirection?: 'asc' | 'desc';
  /** Custom sort function */
  customSort?: (a: OrganizationItem, b: OrganizationItem) => number;
}

/**
 * Virtualization options
 */
export interface VirtualizationOptions {
  /** Height of each item in pixels */
  itemHeight?: number;
  /** Number of items to render outside visible area */
  overscan?: number;
  /** Whether to enable dynamic height calculation */
  dynamicHeight?: boolean;
}

/**
 * Accessibility options
 */
export interface AccessibilityOptions {
  /** ARIA label for the selector */
  ariaLabel?: string;
  /** ARIA description */
  ariaDescription?: string;
  /** Whether to announce selection changes */
  announceChanges?: boolean;
  /** Custom announcement messages */
  announcements?: {
    selected?: (org: OrganizationItem) => string;
    opened?: string;
    closed?: string;
    noResults?: string;
  };
}

/**
 * Animation options
 */
export interface AnimationOptions {
  /** Whether to enable animations */
  enabled?: boolean;
  /** Animation duration in milliseconds */
  duration?: number;
  /** Animation easing function */
  easing?: string;
  /** Whether to animate list items */
  animateItems?: boolean;
}
