// Main component
export { OrganizationSelector } from './OrganizationSelector';

// Sub-components
export { OrganizationDropdown } from './components/OrganizationDropdown';
export { OrganizationList } from './components/OrganizationList';
export { OrganizationItem } from './components/OrganizationItem';
export { OrganizationSearch } from './components/OrganizationSearch';
export { OrganizationActions } from './components/OrganizationActions';

// Hooks
export { useOrganizationSelector } from './hooks/useOrganizationSelector';
export { useKeyboardNavigation } from './hooks/useKeyboardNavigation';
export { useOrganizationSearch } from './hooks/useOrganizationSearch';

// Types
export type {
  OrganizationSelectorProps,
  OrganizationSelectorConfig,
  OrganizationSelectorState,
  OrganizationSelectorCallbacks,
  OrganizationItem,
  KeyboardNavigationState,
  SearchOptions,
  SortOptions,
  VirtualizationOptions,
  AccessibilityOptions,
  AnimationOptions
} from './types/organization-selector-types';

// Utils
export {
  transformToOrganizationItem,
  filterOrganizations,
  sortOrganizations,
  groupOrganizations,
  getOrganizationStats,
  validateOrganization,
  formatOrganizationName,
  getOrganizationPermissions,
  createOrganizationItem
} from './utils/organization-utils';

export {
  getNextNavigableIndex,
  getFirstNavigableIndex,
  getLastNavigableIndex,
  handleKeyboardNavigation,
  scrollIntoView,
  getKeyboardShortcuts,
  isSearchKey,
  debounce
} from './utils/keyboard-utils';
