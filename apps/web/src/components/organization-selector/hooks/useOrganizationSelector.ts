import { Organization } from '@/contexts/auth-context-types';
import { useAuth } from '@/hooks/useAuth';
import { useUserRoles } from '@/hooks/useUserRoles';
import { useCallback, useEffect, useMemo, useState } from 'react';
import {
    OrganizationItem,
    OrganizationSelectorConfig,
    OrganizationSelectorState
} from '../types/organization-selector-types';
import { transformToOrganizationItem } from '../utils/organization-utils';

/**
 * Main hook for organization selector state management
 */
export function useOrganizationSelector(
  config: OrganizationSelectorConfig = {},
  providedOrganizations?: Organization[]
) {
  const { user, organization: currentOrganization, setOrganization } = useAuth();
  const { roles, isSystemAdmin, isAdmin } = useUserRoles();

  // Memoize organization IDs to prevent infinite re-renders
  const organizationIds = useMemo(() => {
    return roles.map(role => role.organization_id);
  }, [roles]);

  const [state, setState] = useState<OrganizationSelectorState>({
    isOpen: false,
    isLoading: false,
    searchTerm: '',
    highlightedIndex: -1,
    organizations: [],
    filteredOrganizations: [],
    selectedOrganizations: [],
    error: null
  });

  // Load organizations
  const loadOrganizations = useCallback(async () => {
    if (providedOrganizations) {
      // Use provided organizations
      const transformedOrgs = providedOrganizations.map(org =>
        transformToOrganizationItem(org, currentOrganization?.id, {
          canEdit: isAdmin,
          canManage: isSystemAdmin,
          isSystemAdmin
        })
      );

      setState(prev => ({
        ...prev,
        organizations: transformedOrgs,
        filteredOrganizations: transformedOrgs,
        isLoading: false
      }));
      return;
    }

    // Load organizations from API
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // This would be replaced with actual API call
      const { supabase } = await import('@/lib/supabase');

      let query = supabase
        .from('organizations')
        .select('*');

      // System admins can see all organizations
      if (!isSystemAdmin && organizationIds.length > 0) {
        // Regular users only see organizations they belong to
        query = query.in('id', organizationIds);
      }

      const { data: organizations, error } = await query;

      if (error) throw error;

      const transformedOrgs = (organizations || []).map(org =>
        transformToOrganizationItem(org, currentOrganization?.id, {
          canEdit: isAdmin,
          canManage: isSystemAdmin,
          isSystemAdmin
        })
      );

      setState(prev => ({
        ...prev,
        organizations: transformedOrgs,
        filteredOrganizations: transformedOrgs,
        isLoading: false
      }));

    } catch (error) {
      console.error('Failed to load organizations:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to load organizations',
        isLoading: false
      }));
    }
  }, [providedOrganizations, currentOrganization?.id, isAdmin, isSystemAdmin, organizationIds]);

  // Load organizations on mount and when dependencies change
  useEffect(() => {
    loadOrganizations();
  }, [loadOrganizations]);

  // Open/close dropdown
  const setIsOpen = useCallback((isOpen: boolean) => {
    setState(prev => ({ ...prev, isOpen }));
  }, []);

  // Update search term and filter organizations
  const setSearchTerm = useCallback((searchTerm: string) => {
    setState(prev => {
      const filteredOrganizations = searchTerm.trim()
        ? prev.organizations.filter(org =>
            org.name.toLowerCase().includes(searchTerm.toLowerCase())
          )
        : prev.organizations;

      return {
        ...prev,
        searchTerm,
        filteredOrganizations,
        highlightedIndex: -1 // Reset highlight when search changes
      };
    });
  }, []);

  // Set highlighted index
  const setHighlightedIndex = useCallback((index: number) => {
    setState(prev => ({ ...prev, highlightedIndex: index }));
  }, []);

  // Select organization
  const selectOrganization = useCallback(async (org: OrganizationItem) => {
    if (!user || org.disabled) return;

    setState(prev => ({ ...prev, isLoading: true }));

    try {
      // Update auth context (this handles caching automatically)
      setOrganization(org);

      // Update selected organizations
      setState(prev => ({
        ...prev,
        selectedOrganizations: config?.multiSelect
          ? [...prev.selectedOrganizations, org]
          : [org],
        isOpen: false
      }));

      // Show success message
      const { toast } = await import('sonner');
      toast.success(`Switched to ${org.name}`);

      // Refresh page to ensure all components update
      setTimeout(() => {
        window.location.href = '/dashboard';
      }, 300);

    } catch (error) {
      console.error('Failed to select organization:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to select organization',
        isLoading: false
      }));

      const { toast } = await import('sonner');
      toast.error('Failed to switch organization');
    }
  }, [user, setOrganization, config?.multiSelect]);

  // Clear selection
  const clearSelection = useCallback(() => {
    setState(prev => ({
      ...prev,
      selectedOrganizations: []
    }));
  }, []);

  // Refresh organizations
  const refresh = useCallback(() => {
    loadOrganizations();
  }, [loadOrganizations]);

  // Clear error
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Get permissions for current user
  const canSwitchOrganizations = isAdmin && (isSystemAdmin || state.organizations.length > 1);
  const canCreateOrganization = isSystemAdmin;
  const canManageOrganizations = isSystemAdmin;

  return {
    // State
    ...state,
    currentOrganization,

    // Computed values
    canSwitchOrganizations,
    canCreateOrganization,
    canManageOrganizations,
    hasMultipleOrganizations: state.organizations.length > 1,
    isEmpty: state.organizations.length === 0,

    // Actions
    setIsOpen,
    setSearchTerm,
    setHighlightedIndex,
    selectOrganization,
    clearSelection,
    refresh,
    clearError,

    // User info
    user,
    userRoles: roles,
    isSystemAdmin,
    isAdmin
  };
}
