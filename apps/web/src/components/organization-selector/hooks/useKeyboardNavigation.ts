import { useCallback, useEffect, useRef, useState } from 'react';
import { OrganizationItem, KeyboardNavigationState } from '../types/organization-selector-types';
import { handleKeyboardNavigation, scrollIntoView } from '../utils/keyboard-utils';

/**
 * Hook for managing keyboard navigation in organization selector
 */
export function useKeyboardNavigation(
  items: OrganizationItem[],
  isOpen: boolean,
  onSelect?: (item: OrganizationItem) => void,
  onClose?: () => void
) {
  const [state, setState] = useState<KeyboardNavigationState>({
    focusedIndex: -1,
    isActive: false,
    lastDirection: null
  });

  const containerRef = useRef<HTMLElement>(null);
  const itemRefs = useRef<Map<number, HTMLElement>>(new Map());

  // Reset navigation when dropdown opens/closes or items change
  useEffect(() => {
    if (!isOpen) {
      setState(prev => ({
        ...prev,
        focusedIndex: -1,
        isActive: false,
        lastDirection: null
      }));
    } else {
      // Set initial focus to first non-disabled item
      const firstNavigableIndex = items.findIndex(item => !item.disabled);
      setState(prev => ({
        ...prev,
        focusedIndex: firstNavigableIndex,
        isActive: true
      }));
    }
  }, [isOpen, items]);

  // Scroll focused item into view
  useEffect(() => {
    if (state.focusedIndex >= 0 && state.isActive) {
      const focusedElement = itemRefs.current.get(state.focusedIndex);
      const container = containerRef.current;
      
      if (focusedElement && container) {
        scrollIntoView(focusedElement, container, {
          behavior: 'smooth',
          block: 'nearest'
        });
      }
    }
  }, [state.focusedIndex, state.isActive]);

  // Handle keyboard events
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!isOpen || !state.isActive) return;

    const result = handleKeyboardNavigation(
      event.key,
      state.focusedIndex,
      items,
      {
        skipDisabled: true,
        pageSize: 10
      }
    );

    switch (result.action) {
      case 'navigate':
        if (result.newIndex !== state.focusedIndex) {
          event.preventDefault();
          setState(prev => ({
            ...prev,
            focusedIndex: result.newIndex,
            lastDirection: event.key === 'ArrowDown' ? 'down' : 'up'
          }));
        }
        break;

      case 'select':
        event.preventDefault();
        if (state.focusedIndex >= 0 && state.focusedIndex < items.length) {
          const selectedItem = items[state.focusedIndex];
          if (selectedItem && !selectedItem.disabled) {
            onSelect?.(selectedItem);
          }
        }
        break;

      case 'close':
        event.preventDefault();
        onClose?.();
        break;
    }
  }, [isOpen, state.isActive, state.focusedIndex, items, onSelect, onClose]);

  // Attach/detach keyboard event listeners
  useEffect(() => {
    if (isOpen && state.isActive) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isOpen, state.isActive, handleKeyDown]);

  // Register item ref
  const registerItemRef = useCallback((index: number, element: HTMLElement | null) => {
    if (element) {
      itemRefs.current.set(index, element);
    } else {
      itemRefs.current.delete(index);
    }
  }, []);

  // Set container ref
  const setContainerRef = useCallback((element: HTMLElement | null) => {
    containerRef.current = element;
  }, []);

  // Navigate to specific index
  const navigateToIndex = useCallback((index: number) => {
    if (index >= 0 && index < items.length && !items[index]?.disabled) {
      setState(prev => ({
        ...prev,
        focusedIndex: index
      }));
    }
  }, [items]);

  // Navigate by direction
  const navigate = useCallback((direction: 'up' | 'down' | 'first' | 'last') => {
    const keyMap = {
      up: 'ArrowUp',
      down: 'ArrowDown',
      first: 'Home',
      last: 'End'
    };

    const result = handleKeyboardNavigation(
      keyMap[direction],
      state.focusedIndex,
      items,
      { skipDisabled: true }
    );

    if (result.action === 'navigate' && result.newIndex !== state.focusedIndex) {
      setState(prev => ({
        ...prev,
        focusedIndex: result.newIndex,
        lastDirection: direction === 'down' ? 'down' : 'up'
      }));
    }
  }, [state.focusedIndex, items]);

  // Select current item
  const selectCurrent = useCallback(() => {
    if (state.focusedIndex >= 0 && state.focusedIndex < items.length) {
      const selectedItem = items[state.focusedIndex];
      if (selectedItem && !selectedItem.disabled) {
        onSelect?.(selectedItem);
      }
    }
  }, [state.focusedIndex, items, onSelect]);

  // Activate/deactivate navigation
  const setActive = useCallback((active: boolean) => {
    setState(prev => ({
      ...prev,
      isActive: active
    }));
  }, []);

  // Get item props for accessibility
  const getItemProps = useCallback((index: number) => {
    const isFocused = state.focusedIndex === index;
    const item = items[index];
    
    return {
      ref: (el: HTMLElement | null) => registerItemRef(index, el),
      'data-index': index,
      'data-focused': isFocused,
      'aria-selected': isFocused,
      'aria-disabled': item?.disabled,
      tabIndex: isFocused ? 0 : -1,
      role: 'option'
    };
  }, [state.focusedIndex, items, registerItemRef]);

  // Get container props for accessibility
  const getContainerProps = useCallback(() => ({
    ref: setContainerRef,
    role: 'listbox',
    'aria-activedescendant': state.focusedIndex >= 0 ? `org-item-${state.focusedIndex}` : undefined,
    'aria-label': 'Organization list'
  }), [state.focusedIndex, setContainerRef]);

  return {
    // State
    focusedIndex: state.focusedIndex,
    isActive: state.isActive,
    lastDirection: state.lastDirection,

    // Actions
    navigateToIndex,
    navigate,
    selectCurrent,
    setActive,

    // Props getters
    getItemProps,
    getContainerProps,

    // Refs
    containerRef,
    itemRefs: itemRefs.current
  };
}
