import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Edit, Settings, Building2 } from 'lucide-react';
import { OrganizationItem as OrganizationItemType } from '../types/organization-selector-types';
import { cn } from '@/lib/utils';

interface OrganizationItemProps {
  organization: OrganizationItemType;
  isHighlighted?: boolean;
  isFocused?: boolean;
  showActions?: boolean;
  showCurrentIndicator?: boolean;
  onSelect?: (org: OrganizationItemType) => void;
  onEdit?: (org: OrganizationItemType) => void;
  onManage?: (org: OrganizationItemType) => void;
  highlightText?: (text: string) => React.ReactNode;
  className?: string;
}

export function OrganizationItem({
  organization,
  isHighlighted = false,
  isFocused = false,
  showActions = true,
  showCurrentIndicator = true,
  onSelect,
  onEdit,
  onManage,
  highlightText,
  className
}: OrganizationItemProps) {
  const handleSelect = (e: React.MouseEvent) => {
    // Don't trigger selection if clicking on action buttons
    if ((e.target as HTMLElement).closest('[data-action]')) {
      return;
    }
    
    if (!organization.disabled) {
      onSelect?.(organization);
    }
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onEdit?.(organization);
  };

  const handleManage = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onManage?.(organization);
  };

  const displayName = highlightText 
    ? highlightText(organization.name)
    : organization.name;

  return (
    <div
      className={cn(
        'flex items-center justify-between px-3 py-2 cursor-pointer transition-colors',
        'hover:bg-accent/50 focus:bg-accent focus:outline-none',
        {
          'bg-accent': isHighlighted,
          'bg-accent/70': isFocused,
          'opacity-50 cursor-not-allowed': organization.disabled,
          'bg-primary/10 border-l-2 border-l-primary': organization.isCurrent && showCurrentIndicator
        },
        className
      )}
      onClick={handleSelect}
      role="option"
      aria-selected={isFocused}
      aria-disabled={organization.disabled}
      data-testid={`organization-item-${organization.id}`}
    >
      {/* Organization Info */}
      <div className="flex items-center flex-1 min-w-0">
        <Building2 className="h-4 w-4 text-muted-foreground mr-2 flex-shrink-0" />
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <span className="font-medium truncate">
              {displayName}
            </span>
            
            {/* Current Organization Badge */}
            {organization.isCurrent && showCurrentIndicator && (
              <Badge variant="secondary" className="text-xs">
                Current
              </Badge>
            )}
          </div>
          
          {/* Organization Metadata */}
          {organization.metadata?.userRole && (
            <div className="text-xs text-muted-foreground mt-0.5">
              Role: {organization.metadata.userRole}
            </div>
          )}
        </div>
      </div>

      {/* Actions */}
      {showActions && (organization.canEdit || organization.canManage) && (
        <div className="flex items-center gap-1 ml-2" data-action="true">
          {organization.canEdit && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 opacity-60 hover:opacity-100"
              onClick={handleEdit}
              title="Edit organization"
              data-action="edit"
            >
              <Edit className="h-3 w-3" />
            </Button>
          )}
          
          {organization.canManage && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 opacity-60 hover:opacity-100"
              onClick={handleManage}
              title="Manage organization"
              data-action="manage"
            >
              <Settings className="h-3 w-3" />
            </Button>
          )}
        </div>
      )}
    </div>
  );
}
