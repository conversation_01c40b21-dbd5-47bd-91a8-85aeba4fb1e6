import React from 'react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Plus, Settings, Table } from 'lucide-react';
import { cn } from '@/lib/utils';

interface OrganizationActionsProps {
  canCreate?: boolean;
  canManageAll?: boolean;
  onCreate?: () => void;
  onManageAll?: () => void;
  onViewAll?: () => void;
  className?: string;
  showSeparator?: boolean;
}

export function OrganizationActions({
  canCreate = false,
  canManageAll = false,
  onCreate,
  onManageAll,
  onViewAll,
  className,
  showSeparator = true
}: OrganizationActionsProps) {
  const hasActions = canCreate || canManageAll || onViewAll;

  if (!hasActions) {
    return null;
  }

  return (
    <div className={cn('py-1', className)}>
      {showSeparator && (
        <div className="px-3 py-1">
          <Separator />
        </div>
      )}
      
      <div className="px-2 space-y-1">
        {/* Create New Organization */}
        {canCreate && onCreate && (
          <Button
            variant="ghost"
            size="sm"
            className="w-full justify-start h-8 px-2 text-sm"
            onClick={onCreate}
          >
            <Plus className="h-4 w-4 mr-2" />
            Create New Organization
          </Button>
        )}

        {/* View All Organizations */}
        {onViewAll && (
          <Button
            variant="ghost"
            size="sm"
            className="w-full justify-start h-8 px-2 text-sm"
            onClick={onViewAll}
          >
            <Table className="h-4 w-4 mr-2" />
            View All Organizations
          </Button>
        )}

        {/* Manage All Organizations */}
        {canManageAll && onManageAll && (
          <Button
            variant="ghost"
            size="sm"
            className="w-full justify-start h-8 px-2 text-sm"
            onClick={onManageAll}
          >
            <Settings className="h-4 w-4 mr-2" />
            Manage Organizations
          </Button>
        )}
      </div>
    </div>
  );
}
