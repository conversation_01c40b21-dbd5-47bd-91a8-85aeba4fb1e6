import React from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { OrganizationItem } from './OrganizationItem';
import { OrganizationItem as OrganizationItemType } from '../types/organization-selector-types';
import { cn } from '@/lib/utils';

interface OrganizationListProps {
  organizations: OrganizationItemType[];
  highlightedIndex?: number;
  focusedIndex?: number;
  showActions?: boolean;
  showCurrentIndicator?: boolean;
  maxHeight?: number;
  onSelect?: (org: OrganizationItemType) => void;
  onEdit?: (org: OrganizationItemType) => void;
  onManage?: (org: OrganizationItemType) => void;
  highlightText?: (text: string) => React.ReactNode;
  className?: string;
  emptyMessage?: string;
  getItemProps?: (index: number) => Record<string, any>;
  getContainerProps?: () => Record<string, any>;
}

export function OrganizationList({
  organizations,
  highlightedIndex = -1,
  focusedIndex = -1,
  showActions = true,
  showCurrentIndicator = true,
  maxHeight = 300,
  onSelect,
  onEdit,
  onManage,
  highlightText,
  className,
  emptyMessage = 'No organizations found',
  getItemProps,
  getContainerProps
}: OrganizationListProps) {
  
  // Group organizations: current first, then alphabetical
  const groupedOrganizations = React.useMemo(() => {
    const current = organizations.filter(org => org.isCurrent);
    const others = organizations.filter(org => !org.isCurrent);
    
    return {
      current,
      others,
      all: [...current, ...others]
    };
  }, [organizations]);

  if (organizations.length === 0) {
    return (
      <div className={cn('px-3 py-8 text-center text-sm text-muted-foreground', className)}>
        {emptyMessage}
      </div>
    );
  }

  const containerProps = getContainerProps?.() || {};

  return (
    <ScrollArea 
      className={cn('w-full', className)} 
      style={{ maxHeight: `${maxHeight}px` }}
    >
      <div 
        {...containerProps}
        className="py-1"
        role="listbox"
        aria-label="Organization list"
      >
        {/* Current Organization Section */}
        {groupedOrganizations.current.length > 0 && (
          <>
            {groupedOrganizations.current.map((org, index) => {
              const globalIndex = index;
              const itemProps = getItemProps?.(globalIndex) || {};
              
              return (
                <OrganizationItem
                  key={org.id}
                  organization={org}
                  isHighlighted={highlightedIndex === globalIndex}
                  isFocused={focusedIndex === globalIndex}
                  showActions={showActions}
                  showCurrentIndicator={showCurrentIndicator}
                  onSelect={onSelect}
                  onEdit={onEdit}
                  onManage={onManage}
                  highlightText={highlightText}
                  {...itemProps}
                />
              );
            })}
            
            {/* Separator if there are other organizations */}
            {groupedOrganizations.others.length > 0 && (
              <div className="px-3 py-1">
                <Separator />
              </div>
            )}
          </>
        )}

        {/* Other Organizations Section */}
        {groupedOrganizations.others.map((org, index) => {
          const globalIndex = groupedOrganizations.current.length + index;
          const itemProps = getItemProps?.(globalIndex) || {};
          
          return (
            <OrganizationItem
              key={org.id}
              organization={org}
              isHighlighted={highlightedIndex === globalIndex}
              isFocused={focusedIndex === globalIndex}
              showActions={showActions}
              showCurrentIndicator={showCurrentIndicator}
              onSelect={onSelect}
              onEdit={onEdit}
              onManage={onManage}
              highlightText={highlightText}
              {...itemProps}
            />
          );
        })}
      </div>
    </ScrollArea>
  );
}
