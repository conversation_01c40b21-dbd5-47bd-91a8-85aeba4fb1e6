import React, { useRef, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface OrganizationSearchProps {
  value: string;
  placeholder?: string;
  onChange: (value: string) => void;
  onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  onClear?: () => void;
  autoFocus?: boolean;
  disabled?: boolean;
  className?: string;
  showClearButton?: boolean;
  searchStats?: {
    totalCount: number;
    filteredCount: number;
    hasSearch: boolean;
  };
}

export function OrganizationSearch({
  value,
  placeholder = 'Search organizations...',
  onChange,
  onKeyDown,
  onClear,
  autoFocus = false,
  disabled = false,
  className,
  showClearButton = true,
  searchStats
}: OrganizationSearchProps) {
  const inputRef = useRef<HTMLInputElement>(null);

  // Auto focus when component mounts
  useEffect(() => {
    if (autoFocus && inputRef.current) {
      // Small delay to ensure dropdown is fully rendered
      const timer = setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [autoFocus]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Handle clear on Escape
    if (e.key === 'Escape' && value) {
      e.stopPropagation();
      onClear?.();
      return;
    }
    
    onKeyDown?.(e);
  };

  const handleClear = () => {
    onClear?.();
    inputRef.current?.focus();
  };

  return (
    <div className={cn('space-y-2', className)}>
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground pointer-events-none" />
        
        <Input
          ref={inputRef}
          type="text"
          value={value}
          placeholder={placeholder}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          disabled={disabled}
          className="pl-9 pr-9 h-9"
          autoComplete="off"
          spellCheck={false}
          role="searchbox"
          aria-label="Search organizations"
          aria-expanded="true"
          aria-autocomplete="list"
        />
        
        {/* Clear Button */}
        {showClearButton && value && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0 hover:bg-muted"
            onClick={handleClear}
            title="Clear search"
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>

      {/* Search Stats */}
      {searchStats && searchStats.hasSearch && (
        <div className="px-3 py-1 text-xs text-muted-foreground">
          {searchStats.filteredCount === 0 ? (
            'No organizations found'
          ) : searchStats.filteredCount === searchStats.totalCount ? (
            `Showing all ${searchStats.totalCount} organizations`
          ) : (
            `Showing ${searchStats.filteredCount} of ${searchStats.totalCount} organizations`
          )}
        </div>
      )}
    </div>
  );
}
