import { OrganizationItem } from '../types/organization-selector-types';

/**
 * Keyboard navigation utilities for organization selector
 */

/**
 * Get the next navigable index in a list
 */
export function getNextNavigableIndex(
  currentIndex: number,
  items: OrganizationItem[],
  direction: 'up' | 'down',
  skipDisabled: boolean = true
): number {
  if (items.length === 0) return -1;

  const increment = direction === 'down' ? 1 : -1;
  let nextIndex = currentIndex;
  let attempts = 0;
  const maxAttempts = items.length;

  do {
    nextIndex = nextIndex + increment;
    
    // Wrap around
    if (nextIndex >= items.length) {
      nextIndex = 0;
    } else if (nextIndex < 0) {
      nextIndex = items.length - 1;
    }

    attempts++;
    
    // Prevent infinite loop
    if (attempts >= maxAttempts) {
      return currentIndex;
    }

    // If we don't need to skip disabled items, return immediately
    if (!skipDisabled) {
      return nextIndex;
    }

    // Check if the item at this index is navigable
    const item = items[nextIndex];
    if (item && !item.disabled) {
      return nextIndex;
    }

  } while (nextIndex !== currentIndex);

  // If we've cycled through all items and none are navigable, return current
  return currentIndex;
}

/**
 * Get the first navigable index in a list
 */
export function getFirstNavigableIndex(
  items: OrganizationItem[],
  skipDisabled: boolean = true
): number {
  if (items.length === 0) return -1;

  if (!skipDisabled) return 0;

  for (let i = 0; i < items.length; i++) {
    const item = items[i];
    if (item && !item.disabled) {
      return i;
    }
  }

  return -1;
}

/**
 * Get the last navigable index in a list
 */
export function getLastNavigableIndex(
  items: OrganizationItem[],
  skipDisabled: boolean = true
): number {
  if (items.length === 0) return -1;

  if (!skipDisabled) return items.length - 1;

  for (let i = items.length - 1; i >= 0; i--) {
    const item = items[i];
    if (item && !item.disabled) {
      return i;
    }
  }

  return -1;
}

/**
 * Handle keyboard navigation for organization selector
 */
export function handleKeyboardNavigation(
  key: string,
  currentIndex: number,
  items: OrganizationItem[],
  options: {
    skipDisabled?: boolean;
    pageSize?: number;
  } = {}
): {
  newIndex: number;
  action: 'navigate' | 'select' | 'close' | 'none';
} {
  const { skipDisabled = true, pageSize = 10 } = options;

  switch (key) {
    case 'ArrowDown':
      return {
        newIndex: getNextNavigableIndex(currentIndex, items, 'down', skipDisabled),
        action: 'navigate'
      };

    case 'ArrowUp':
      return {
        newIndex: getNextNavigableIndex(currentIndex, items, 'up', skipDisabled),
        action: 'navigate'
      };

    case 'Home':
      return {
        newIndex: getFirstNavigableIndex(items, skipDisabled),
        action: 'navigate'
      };

    case 'End':
      return {
        newIndex: getLastNavigableIndex(items, skipDisabled),
        action: 'navigate'
      };

    case 'PageDown':
      const pageDownIndex = Math.min(currentIndex + pageSize, items.length - 1);
      return {
        newIndex: getNextNavigableIndex(pageDownIndex - 1, items, 'down', skipDisabled),
        action: 'navigate'
      };

    case 'PageUp':
      const pageUpIndex = Math.max(currentIndex - pageSize, 0);
      return {
        newIndex: getNextNavigableIndex(pageUpIndex + 1, items, 'up', skipDisabled),
        action: 'navigate'
      };

    case 'Enter':
    case ' ':
      return {
        newIndex: currentIndex,
        action: 'select'
      };

    case 'Escape':
      return {
        newIndex: currentIndex,
        action: 'close'
      };

    default:
      return {
        newIndex: currentIndex,
        action: 'none'
      };
  }
}

/**
 * Scroll element into view with smooth behavior
 */
export function scrollIntoView(
  element: HTMLElement | null,
  container: HTMLElement | null,
  options: {
    behavior?: 'auto' | 'smooth';
    block?: 'start' | 'center' | 'end' | 'nearest';
    inline?: 'start' | 'center' | 'end' | 'nearest';
  } = {}
): void {
  if (!element || !container) return;

  const { behavior = 'smooth', block = 'nearest', inline = 'nearest' } = options;

  // Check if element is already in view
  const containerRect = container.getBoundingClientRect();
  const elementRect = element.getBoundingClientRect();

  const isInView = 
    elementRect.top >= containerRect.top &&
    elementRect.bottom <= containerRect.bottom;

  if (!isInView) {
    element.scrollIntoView({
      behavior,
      block,
      inline
    });
  }
}

/**
 * Get keyboard shortcut description for accessibility
 */
export function getKeyboardShortcuts(): Array<{
  key: string;
  description: string;
}> {
  return [
    { key: 'Arrow Keys', description: 'Navigate through organizations' },
    { key: 'Home', description: 'Go to first organization' },
    { key: 'End', description: 'Go to last organization' },
    { key: 'Page Up/Down', description: 'Navigate by page' },
    { key: 'Enter/Space', description: 'Select organization' },
    { key: 'Escape', description: 'Close dropdown' },
    { key: 'Type to search', description: 'Filter organizations' }
  ];
}

/**
 * Check if a key should trigger search
 */
export function isSearchKey(key: string): boolean {
  // Allow alphanumeric characters and common symbols for search
  return /^[a-zA-Z0-9\s\-_@.]$/.test(key) && key.length === 1;
}

/**
 * Debounce function for search input
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}
