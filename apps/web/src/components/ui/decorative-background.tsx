"use client"

import { cn } from "@/lib/utils"
import React from "react"

interface DecorativeBackgroundProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "auth"
  className?: string
}

export function DecorativeBackground({
  variant = "default",
  className,
  ...props
}: DecorativeBackgroundProps) {
  return (
    <div
      className={cn(
        "absolute inset-0 overflow-hidden",
        variant === "auth" && "bg-primary",
        className
      )}
      {...props}
    >
      {/* Modern gradient pattern */}
      <div className="absolute inset-0">
        <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <linearGradient id="main-gradient" x1="0" y1="0" x2="1" y2="1">
              <stop offset="0%" stopColor="currentColor" stopOpacity="0.12" />
              <stop offset="50%" stopColor="currentColor" stopOpacity="0.08" />
              <stop offset="100%" stopColor="currentColor" stopOpacity="0.12" />
            </linearGradient>
            <pattern id="dots" width="40" height="40" patternUnits="userSpaceOnUse">
              <circle cx="2" cy="2" r="1" fill="currentColor" fillOpacity="0.07" />
            </pattern>
            <filter id="glow">
              <feGaussianBlur stdDeviation="4" result="blur" />
              <feComposite in="SourceGraphic" in2="blur" operator="over" />
            </filter>
          </defs>
          <rect width="100%" height="100%" fill="url(#main-gradient)" className="text-primary-foreground" />
          <rect width="100%" height="100%" fill="url(#dots)" className="text-primary-foreground" />
        </svg>
      </div>

      {/* Layered gradient overlays */}
      <div className="absolute inset-0">
        {/* Top-left to bottom-right gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary-foreground/10 via-transparent to-primary/20"></div>
        
        {/* Top-right to bottom-left gradient */}
        <div className="absolute inset-0 bg-gradient-to-bl from-transparent via-primary/5 to-primary-foreground/10"></div>
        
        {/* Radial gradient in the center */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,_var(--tw-gradient-stops))] from-primary-foreground/5 via-transparent to-transparent"></div>
      </div>
      
      {/* Edge highlights */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-full h-64 bg-gradient-to-b from-primary-foreground/10 to-transparent"></div>
        <div className="absolute bottom-0 left-0 w-full h-64 bg-gradient-to-t from-primary-foreground/10 to-transparent"></div>
        <div className="absolute left-0 top-0 w-64 h-full bg-gradient-to-r from-primary-foreground/10 to-transparent"></div>
        <div className="absolute right-0 top-0 w-64 h-full bg-gradient-to-l from-primary-foreground/10 to-transparent"></div>
      </div>
      
      {/* Animated gradient overlay */}
      <div className="absolute inset-0 opacity-30 mix-blend-soft-light animate-gradient-shift">
        <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(68,68,68,.2)_50%,transparent_75%,transparent_100%)] bg-[length:250%_250%]"></div>
      </div>
    </div>
  )
}
