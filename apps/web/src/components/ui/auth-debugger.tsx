import { useAuth } from '@/hooks/useAuth';
import { Button } from './button';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/lib/supabase';
import type { Session } from '@supabase/supabase-js';

interface SessionData {
  session: Session | null;
}

export function AuthDebugger() {
  const { user, organization, isLoading } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [sessionData, setSessionData] = useState<SessionData | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    if (isOpen) {
      // Get session data when panel is opened
      supabase.auth.getSession().then(({ data }) => {
        setSessionData(data);
      });
    }
  }, [isOpen]);

  if (isLoading) return null;

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className="bg-background/80 backdrop-blur-sm"
      >
        {isOpen ? 'Close Debug' : 'Debug Auth'}
      </Button>

      {isOpen && (
        <div className="fixed bottom-16 right-4 w-96 p-4 bg-background/90 backdrop-blur-sm rounded-md border shadow-lg overflow-auto max-h-[80vh]">
          <h3 className="font-semibold text-lg mb-2">Auth Debug Panel</h3>
          
          <div className="space-y-4">
            <div>
              <h4 className="font-medium">User:</h4>
              <pre className="text-xs bg-muted p-2 rounded-sm overflow-auto max-h-28">
                {user ? JSON.stringify(user, null, 2) : 'Not logged in'}
              </pre>
            </div>

            <div>
              <h4 className="font-medium">Organization:</h4>
              <pre className="text-xs bg-muted p-2 rounded-sm overflow-auto max-h-28">
                {organization ? JSON.stringify(organization, null, 2) : 'No organization'}
              </pre>
            </div>

            <div>
              <h4 className="font-medium">Session:</h4>
              <pre className="text-xs bg-muted p-2 rounded-sm overflow-auto max-h-28">
                {sessionData ? JSON.stringify(sessionData, null, 2) : 'Loading...'}
              </pre>
            </div>

            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  supabase.auth.refreshSession();
                  supabase.auth.getSession().then(({ data }) => {
                    setSessionData(data);
                    window.location.reload();
                  });
                }}
              >
                Refresh Session
              </Button>
              
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => {
                  supabase.auth.signOut().then(() => {
                    navigate('/login');
                    window.location.reload();
                  });
                }}
              >
                Force Sign Out
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 