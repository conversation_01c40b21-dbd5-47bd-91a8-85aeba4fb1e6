import { useEffect, useState } from "react";
import { Toast, ToastClose, ToastDescription, ToastTitle } from "./toast";

interface ToastProps {
  id: string;
  title?: string;
  description?: string;
  variant?: "default" | "error" | "success" | "warning" | "info";
  duration?: number;
}

export function Toaster() {
  const [toasts, setToasts] = useState<ToastProps[]>([]);

  useEffect(() => {
    const handleToast = (event: CustomEvent<ToastProps>) => {
      const { id, title, description, variant, duration } = event.detail;
      
      setToasts((prev) => [...prev, { id, title, description, variant, duration }]);
      
      if (duration) {
        setTimeout(() => {
          removeToast(id);
        }, duration);
      }
    };

    const removeToast = (id: string) => {
      setToasts((prev) => prev.filter((toast) => toast.id !== id));
    };

    document.addEventListener("toast", handleToast as EventListener);
    document.addEventListener("removeToast", 
      ((e: CustomEvent) => removeToast(e.detail)) as EventListener);

    return () => {
      document.removeEventListener("toast", handleToast as EventListener);
      document.removeEventListener("removeToast", 
        ((e: CustomEvent) => removeToast(e.detail)) as EventListener);
    };
  }, []);

  return (
    <div className="fixed top-0 right-0 z-50 flex flex-col p-4 space-y-4 w-full max-w-md">
      {toasts.map(({ id, title, description, variant }) => (
        <Toast key={id} variant={variant}>
          <div className="flex flex-col space-y-1">
            {title && <ToastTitle>{title}</ToastTitle>}
            {description && <ToastDescription>{description}</ToastDescription>}
          </div>
          <ToastClose onClick={() => setToasts((prev) => prev.filter((t) => t.id !== id))} />
        </Toast>
      ))}
    </div>
  );
} 