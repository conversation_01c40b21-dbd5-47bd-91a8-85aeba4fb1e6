import { useCallback } from "react";

type ToastType = "default" | "error" | "success" | "warning" | "info";

interface ToastProps {
  title: string;
  description: string;
  variant?: ToastType;
  duration?: number;
}

interface ToastContextType {
  toast: (props: ToastProps) => void;
  error: (props: Omit<ToastProps, "variant">) => void;
  success: (props: Omit<ToastProps, "variant">) => void;
  warning: (props: Omit<ToastProps, "variant">) => void;
  info: (props: Omit<ToastProps, "variant">) => void;
}

// Toast implementation using custom event system
export function useToast(): ToastContextType {
  const toast = useCallback(({ title, description, variant = "default", duration = 5000 }: ToastProps) => {
    // Create a unique ID for this toast
    const id = `toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    // Dispatch a custom event with the toast data
    const event = new CustomEvent('toast', {
      detail: {
        id,
        title,
        description,
        variant,
        duration
      }
    });
    
    document.dispatchEvent(event);
  }, []);

  return { 
    toast,
    // Convenience methods for different toast types
    error: (props: Omit<ToastProps, "variant">) => toast({ ...props, variant: "error" }),
    success: (props: Omit<ToastProps, "variant">) => toast({ ...props, variant: "success" }),
    warning: (props: Omit<ToastProps, "variant">) => toast({ ...props, variant: "warning" }),
    info: (props: Omit<ToastProps, "variant">) => toast({ ...props, variant: "info" }),
  };
} 