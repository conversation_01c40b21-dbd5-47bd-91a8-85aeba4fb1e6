import { Button } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { KeyBinding, useShortcuts } from "@/lib/shortcuts";
import { Command as CommandPrimitive } from "cmdk";
import { Search, X } from "lucide-react";
import * as React from "react";

interface CommandPaletteProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface ActionGroup {
  id: string;
  title: string;
  bindings: KeyBinding[];
}

interface Action {
  id: string;
  title: string;
  description: string;
  handler: () => void;
  icon?: React.ComponentType<{ className?: string }>;
}

export function CommandPalette({ open, onOpenChange }: CommandPaletteProps) {
  const [query, setQuery] = React.useState("");
  const inputRef = React.useRef<HTMLInputElement>(null);
  const listRef = React.useRef<HTMLDivElement>(null);
  const [pages, setPages] = React.useState<string[]>(["/"]);
  const activePage = pages[pages.length - 1];
  const isHome = activePage === "/";

  const { getActions, getBindings } = useShortcuts();
  const actionsRecord = getActions();
  const bindingsRecord = getBindings();

  // Convert records to arrays
  const actions = Object.entries(actionsRecord).map(([id, binding]) => ({
    id,
    title: binding.description,
    description: binding.description,
    handler: () => {} // Placeholder since we don't have access to handlers
  } as Action));

  const groups = Object.entries(bindingsRecord).reduce((acc, [id, binding]) => {
    const groupId = id.split('.')[0];
    const group = acc.find(g => g.id === groupId);
    if (group) {
      group.bindings.push(binding);
    } else {
      acc.push({
        id: groupId,
        title: groupId.charAt(0).toUpperCase() + groupId.slice(1),
        bindings: [binding]
      });
    }
    return acc;
  }, [] as ActionGroup[]);

  const popPage = React.useCallback(() => {
    setPages((pages) => {
      const x = [...pages];
      x.splice(-1, 1);
      return x;
    });
  }, []);

  const onKeyDown = React.useCallback(
    (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        if (!isHome) {
          e.preventDefault();
          popPage();
        }
      }
    },
    [isHome, popPage]
  );

  React.useEffect(() => {
    if (open) {
      setPages(["/"]);
      setQuery("");
    }
  }, [open]);

  React.useEffect(() => {
    document.addEventListener("keydown", onKeyDown);
    return () => document.removeEventListener("keydown", onKeyDown);
  }, [onKeyDown]);

  const formatShortcut = (shortcut: string[]) => {
    return shortcut.map((key) => {
      switch (key) {
        case "meta":
          return "⌘";
        case "shift":
          return "⇧";
        case "alt":
          return "⌥";
        case "ctrl":
          return "⌃";
        default:
          return key.toUpperCase();
      }
    }).join(" + ");
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="p-0 gap-0 outline-none">
        <CommandPrimitive className="[&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-group]]:px-2 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-14 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5">
          <div className="flex items-center border-b px-4" cmdk-input-wrapper="">
            <Search className="mr-2 h-5 w-5 shrink-0 text-muted-foreground" />
            <CommandPrimitive.Input
              ref={inputRef}
              value={query}
              onValueChange={setQuery}
              className="flex h-14 w-full rounded-md bg-transparent py-3 text-base outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
              placeholder="Type a command or search..."
            />
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 rounded-full hover:bg-accent"
              onClick={() => onOpenChange(false)}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <ScrollArea className="h-[50vh] overflow-y-auto overflow-x-hidden">
            <CommandPrimitive.List ref={listRef} className="px-2 py-4">
              <CommandPrimitive.Empty className="py-6 text-center text-sm text-muted-foreground">
                No results found.
              </CommandPrimitive.Empty>

              {groups.map((group) => (
                <CommandPrimitive.Group key={group.id} heading={group.title}>
                  {group.bindings.map((binding) => {
                    const action = actions.find((a) => a.id === binding.action);
                    if (!action) return null;

                    return (
                      <CommandPrimitive.Item
                        key={binding.action}
                        onSelect={() => {
                          action.handler();
                          onOpenChange(false);
                        }}
                        className="group relative flex cursor-pointer select-none items-center rounded-lg px-3 py-2.5 text-sm outline-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 hover:bg-accent"
                      >
                        {action.icon && (
                          <div className="flex h-7 w-7 items-center justify-center rounded-full bg-primary/10 mr-3 group-hover:bg-primary/20">
                            <action.icon className="h-4 w-4 text-primary" />
                          </div>
                        )}
                        <div className="flex-1">
                          <div className="font-medium">{action.title}</div>
                          <div className="text-xs text-muted-foreground">
                            {action.description}
                          </div>
                        </div>
                        {binding && (
                          <kbd className="pointer-events-none ml-auto flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium sm:text-xs">
                            {formatShortcut([...binding.modifiers, binding.key])}
                          </kbd>
                        )}
                      </CommandPrimitive.Item>
                    );
                  })}
                </CommandPrimitive.Group>
              ))}
            </CommandPrimitive.List>
          </ScrollArea>
        </CommandPrimitive>
      </DialogContent>
    </Dialog>
  );
}