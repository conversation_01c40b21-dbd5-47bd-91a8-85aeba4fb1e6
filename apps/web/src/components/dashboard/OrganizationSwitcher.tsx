import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Organization } from "@/contexts/auth-context-types";
import { useAuth } from "@/hooks/useAuth";
import { useUserRoles } from "@/hooks/useUserRoles";
import { cacheOrganizationData } from "@/lib/auth/organization-cache";
import { EnhancedOrganization } from "@/lib/auth/organization-types";
import { Building2, ChevronDown, Edit, Plus, Search } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";

export function OrganizationSwitcher() {
  const { organization, setOrganization, user } = useAuth();
  const { roles } = useUserRoles();
  const navigate = useNavigate();
  const searchInputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [hasMultipleOrgs, setHasMultipleOrgs] = useState(false);
  const [availableOrgs, setAvailableOrgs] = useState<Organization[]>([]);
  const [filteredOrgs, setFilteredOrgs] = useState<Organization[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const [isOpen, setIsOpen] = useState(false);

  // Check if the user has multiple organizations
  useEffect(() => {
    if (organization) {
      const enhancedOrg = organization as EnhancedOrganization;
      setHasMultipleOrgs(!!enhancedOrg.hasMultipleOrgs);
      const orgs = enhancedOrg.availableOrgs || [];
      setAvailableOrgs(orgs);
      setFilteredOrgs(orgs);
    }
  }, [organization]);

  // Filter organizations based on search term
  useEffect(() => {
    if (!searchTerm) {
      setFilteredOrgs(availableOrgs);
    } else {
      const filtered = availableOrgs.filter(org =>
        org.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        org.type?.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredOrgs(filtered);
    }
    
    // Reset highlighted index when filtered results change
    setHighlightedIndex(-1);
    
    // Maintain focus on search input after filtering
    if (searchInputRef.current && searchTerm) {
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 0);
    }
  }, [searchTerm, availableOrgs]);

  // Keyboard navigation handler
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setHighlightedIndex(prev => 
          prev < filteredOrgs.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setHighlightedIndex(prev => 
          prev > 0 ? prev - 1 : filteredOrgs.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (highlightedIndex >= 0 && highlightedIndex < filteredOrgs.length) {
          const selectedOrg = filteredOrgs[highlightedIndex];
          if (selectedOrg.id !== organization?.id) {
            handleSelectOrganization(selectedOrg);
          }
        }
        break;
      case 'Escape':
        e.preventDefault();
        setIsOpen(false);
        break;
    }
  };

  // Scroll highlighted item into view
  useEffect(() => {
    if (highlightedIndex >= 0 && dropdownRef.current) {
      const highlightedElement = dropdownRef.current.querySelector(
        `[data-index="${highlightedIndex}"]`
      ) as HTMLElement;
      if (highlightedElement) {
        highlightedElement.scrollIntoView({
          block: 'nearest',
          behavior: 'smooth'
        });
      }
    }
  }, [highlightedIndex]);

  // Handle organization selection from dropdown
  const handleSelectOrganization = async (org: Organization) => {
    if (!user || !org) return;

    try {
      // Set the organization in the auth context
      setOrganization(org);

      // Cache the organization with isLastSelected=true
      cacheOrganizationData(user.id, org, { isLastSelected: true });

      // Refresh the current page to apply the new organization context
      window.location.reload();
    } catch (error) {
      console.error('Error switching organization:', error);
    }
  };

  // Navigate to setup page to create a new organization
  const handleCreateNewOrg = () => {
    navigate('/setup');
  };

  // Function to check if user can edit a specific organization
  const canEditOrganization = () => {
    // Only show edit icons for system admins or users with specific org management permissions
    // For now, we'll be conservative and only show for system admins
    return roles.includes('system_admin');
  };

  // Handle edit organization click
  const handleEditOrganization = (e: React.MouseEvent, orgId: string) => {
    e.preventDefault();
    e.stopPropagation();
    navigate(`/organizations/${orgId}/settings`);
  };

  // Handle search input changes
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.stopPropagation();
    setSearchTerm(e.target.value);
  };

  // Handle search input events
  const handleSearchInputEvents = (e: React.KeyboardEvent<HTMLInputElement> | React.MouseEvent<HTMLInputElement>) => {
    e.stopPropagation();
  };

  // If there's no organization, show a button to go to setup
  if (!organization) {
    return (
      <Button
        variant="outline"
        size="sm"
        className="gap-2"
        onClick={() => navigate('/setup')}
      >
        <Building2 className="h-4 w-4" />
        <span>Select Organization</span>
      </Button>
    );
  }

  return (
    <div className="flex items-center gap-2">
      {hasMultipleOrgs ? (
        <DropdownMenu onOpenChange={(open) => {
          setIsOpen(open);
          if (!open) {
            // Delay clearing search term to prevent flash during close animation
            setTimeout(() => {
              setSearchTerm("");
              setHighlightedIndex(-1);
            }, 150);
          } else {
            // Focus the search input when dropdown opens
            setTimeout(() => {
              searchInputRef.current?.focus();
            }, 100);
          }
        }}>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="gap-2">
              <Building2 className="h-4 w-4" />
              <span className="max-w-[150px] truncate">{organization.name}</span>
              <ChevronDown className="h-4 w-4 opacity-50" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-[320px]" onKeyDown={handleKeyDown}>
            <DropdownMenuLabel>Manage Organizations</DropdownMenuLabel>
            <DropdownMenuSeparator />

            {/* Search Input */}
            <div className="px-2 py-1">
              <div className="relative">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground pointer-events-none" />
                <Input
                  ref={searchInputRef}
                  placeholder="Search organizations..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  onKeyDown={handleKeyDown}
                  onMouseDown={handleSearchInputEvents}
                  onClick={handleSearchInputEvents}
                  className="pl-8 h-8"
                  autoComplete="off"
                />
              </div>
            </div>
            <DropdownMenuSeparator />

            {/* Organizations List */}
            <div ref={dropdownRef} className="max-h-[300px] overflow-y-auto">
              {filteredOrgs.map((org, index) => (
                <DropdownMenuItem
                  key={org.id}
                  className={`flex items-center justify-between cursor-pointer ${
                    org.id === organization.id ? 'bg-accent' : ''
                  } ${
                    highlightedIndex === index ? 'bg-accent/50' : ''
                  }`}
                  onSelect={(e) => {
                    e.preventDefault();
                    if (org.id !== organization.id) {
                      handleSelectOrganization(org);
                    }
                  }}
                  onMouseDown={(e) => {
                    // Only trigger if it's not the edit button
                    if ((e.target as HTMLElement).closest('button[data-edit]')) {
                      return;
                    }
                    if (org.id !== organization.id) {
                      handleSelectOrganization(org);
                    }
                  }}
                  onMouseEnter={() => setHighlightedIndex(index)}
                  data-index={index}
                >
                  <div className="flex items-center gap-2 flex-1">
                    <Building2 className="h-4 w-4" />
                    <span className="flex-1 truncate">{org.name}</span>
                    {org.id === organization.id && (
                      <span className="text-xs text-muted-foreground">Current</span>
                    )}
                  </div>
                  {canEditOrganization() && (
                    <Button
                      data-edit="true"
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 ml-2 opacity-60 hover:opacity-100"
                      onMouseDown={(e) => handleEditOrganization(e, org.id)}
                      onClick={(e) => handleEditOrganization(e, org.id)}
                    >
                      <Edit className="h-3 w-3" />
                    </Button>
                  )}
                </DropdownMenuItem>
              ))}

              {filteredOrgs.length === 0 && searchTerm && (
                <div className="px-2 py-4 text-center text-sm text-muted-foreground">
                  No organizations found matching "{searchTerm}"
                </div>
              )}
            </div>

            <DropdownMenuSeparator />

            {/* Management Options */}
            <DropdownMenuItem
              className="flex items-center gap-2 cursor-pointer text-primary"
              onSelect={handleCreateNewOrg}
            >
              <Plus className="h-4 w-4" />
              <span>Create New Organization</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ) : (
        <div className="flex items-center gap-2 px-3 py-1 text-sm border rounded-md bg-background">
          <Building2 className="h-4 w-4 text-muted-foreground" />
          <span className="font-medium">{organization.name}</span>
        </div>
      )}
    </div>
  );
}
