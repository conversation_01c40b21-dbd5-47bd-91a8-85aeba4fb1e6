import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { useActivity } from "@/hooks/dashboard/useActivity";
import { Activity, FileText, MessageSquare, Pill, User } from "lucide-react";
import { useNavigate } from "react-router-dom";

export function RecentActivity() {
  const { activities, isLoading, error } = useActivity({ limit: 4 });
  const navigate = useNavigate();

  // Function to get icon based on resource type
  const getActivityIcon = (resourceType: string) => {
    switch (resourceType) {
      case 'patient':
        return <User className="h-4 w-4 text-blue-500" />;
      case 'appointment':
        return <Activity className="h-4 w-4 text-green-500" />;
      case 'medical_record':
        return <FileText className="h-4 w-4 text-amber-500" />;
      case 'prescription':
        return <Pill className="h-4 w-4 text-purple-500" />;
      case 'message':
        return <MessageSquare className="h-4 w-4 text-indigo-500" />;
      default:
        return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  if (error) {
    return (
      <Card className="shadow-sm">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-medium">Recent Activity</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="flex items-center justify-center p-6">
            <p className="text-sm text-red-500">Failed to load recent activity</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card className="shadow-sm">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-medium">Recent Activity</CardTitle>
            <Skeleton className="h-8 w-16" />
          </div>
          <CardDescription>
            <Skeleton className="h-4 w-40" />
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <div className="divide-y">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="flex items-start gap-4 p-4">
                <Skeleton className="h-8 w-8 rounded-full" />
                <div className="flex-1">
                  <Skeleton className="h-4 w-full max-w-[250px] mb-1" />
                  <Skeleton className="h-3 w-32 mb-1" />
                  <Skeleton className="h-3 w-24" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="shadow-sm">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-medium">Recent Activity</CardTitle>
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-8 text-xs"
            onClick={() => navigate('/activity')}
          >
            View All
          </Button>
        </div>
        <CardDescription>Latest actions in your organization</CardDescription>
      </CardHeader>
      <CardContent className="p-0">
        {activities.length === 0 ? (
          <div className="flex items-center justify-center p-6">
            <p className="text-sm text-muted-foreground">No recent activity found</p>
          </div>
        ) : (
          <div className="divide-y">
            {activities.map((activity) => (
              <div key={activity.id} className="flex items-start gap-4 p-4 hover:bg-muted/50">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10">
                  {getActivityIcon(activity.resource_type)}
                </div>
                <div>
                  <p className="text-sm font-medium">{activity.action}</p>
                  <p className="text-xs text-muted-foreground">
                    By {activity.actor} • {activity.time}
                  </p>
                  {activity.patient && (
                    <p className="text-xs text-muted-foreground mt-1">
                      Patient: {activity.patient}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
