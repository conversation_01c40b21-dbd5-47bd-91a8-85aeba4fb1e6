import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { useAnalytics } from "@/hooks/dashboard/useAnalytics";
import { PieChart } from "lucide-react";

export function PatientDemographics() {
  const { patientDemographics, isLoading, error } = useAnalytics();

  // Function to render a simple bar chart
  const renderBarChart = (data: { label: string; value: number }[]) => {
    const total = data.reduce((sum, item) => sum + item.value, 0);
    
    return (
      <div className="space-y-2">
        {data.map((item, index) => {
          const percentage = total > 0 ? Math.round((item.value / total) * 100) : 0;
          
          return (
            <div key={index} className="space-y-1">
              <div className="flex items-center justify-between text-xs">
                <span>{item.label}</span>
                <span>{percentage}% ({item.value})</span>
              </div>
              <div className="h-2 w-full rounded-full bg-muted overflow-hidden">
                <div 
                  className="h-full rounded-full bg-primary" 
                  style={{ width: `${percentage}%` }}
                />
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  if (error) {
    return (
      <Card className="shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg font-medium">Patient Demographics</CardTitle>
          <CardDescription>Age distribution of patients</CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <p className="text-sm text-red-500">Failed to load demographics data</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card className="shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg font-medium">Patient Demographics</CardTitle>
          <CardDescription>Age distribution of patients</CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <div className="space-y-4">
            <Skeleton className="h-4 w-32 mb-2" />
            <div className="space-y-2">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="space-y-1">
                  <div className="flex items-center justify-between">
                    <Skeleton className="h-3 w-16" />
                    <Skeleton className="h-3 w-12" />
                  </div>
                  <Skeleton className="h-2 w-full" />
                </div>
              ))}
            </div>
            
            <Skeleton className="h-4 w-32 mt-6 mb-2" />
            <div className="space-y-2">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="space-y-1">
                  <div className="flex items-center justify-between">
                    <Skeleton className="h-3 w-16" />
                    <Skeleton className="h-3 w-12" />
                  </div>
                  <Skeleton className="h-2 w-full" />
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Check if we have data to display
  const hasAgeData = patientDemographics.ageGroups.some(group => group.value > 0);
  const hasGenderData = patientDemographics.genderDistribution.some(item => item.value > 0);

  return (
    <Card className="shadow-sm">
      <CardHeader>
        <CardTitle className="text-lg font-medium">Patient Demographics</CardTitle>
        <CardDescription>Age and gender distribution</CardDescription>
      </CardHeader>
      <CardContent className="p-6">
        {!hasAgeData && !hasGenderData ? (
          <div className="flex flex-col items-center justify-center h-60 gap-4">
            <PieChart className="h-16 w-16 text-muted-foreground" />
            <p className="text-muted-foreground text-center">
              Not enough patient data to display demographics.
              <br />
              Add more patients to see statistics.
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {hasAgeData && (
              <div>
                <h4 className="text-sm font-medium mb-3">Age Distribution</h4>
                {renderBarChart(patientDemographics.ageGroups)}
              </div>
            )}
            
            {hasGenderData && (
              <div>
                <h4 className="text-sm font-medium mb-3">Gender Distribution</h4>
                {renderBarChart(patientDemographics.genderDistribution)}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
