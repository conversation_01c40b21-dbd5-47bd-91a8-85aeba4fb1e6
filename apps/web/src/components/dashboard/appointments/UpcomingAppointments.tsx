import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { useAppointments } from "@/hooks/dashboard/useAppointments";
import { Calendar, Clock, FileEdit, User } from "lucide-react";
import { useNavigate } from "react-router-dom";

export function UpcomingAppointments() {
  const { appointments, isLoading, error } = useAppointments({ 
    limit: 4, 
    upcomingOnly: true 
  });
  const navigate = useNavigate();

  // Function to get appointment type badge variant
  const getTypeVariant = (type: string) => {
    switch (type) {
      case 'Annual Physical':
        return 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300';
      case 'Follow-up':
        return 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300';
      case 'Consultation':
        return 'bg-amber-100 text-amber-600 dark:bg-amber-900 dark:text-amber-300';
      case 'Vaccination':
        return 'bg-purple-100 text-purple-600 dark:bg-purple-900 dark:text-purple-300';
      case 'Lab Work':
        return 'bg-indigo-100 text-indigo-600 dark:bg-indigo-900 dark:text-indigo-300';
      case 'Sick Visit':
        return 'bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-300';
    }
  };

  // Function to format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const isToday = date >= today && date < tomorrow;
    const isTomorrow = date >= tomorrow && date < new Date(tomorrow.getTime() + 86400000);
    
    if (isToday) {
      return 'Today';
    } else if (isTomorrow) {
      return 'Tomorrow';
    } else {
      return date.toLocaleDateString('en-US', { 
        weekday: 'short',
        month: 'short', 
        day: 'numeric' 
      });
    }
  };

  if (error) {
    return (
      <Card className="shadow-sm">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-medium">Upcoming Appointments</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="flex items-center justify-center p-6">
            <p className="text-sm text-red-500">Failed to load upcoming appointments</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card className="shadow-sm">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-medium">Upcoming Appointments</CardTitle>
            <Skeleton className="h-8 w-16" />
          </div>
          <CardDescription>
            <Skeleton className="h-4 w-40" />
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <div className="divide-y">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="flex items-center justify-between p-4">
                <div className="flex items-center gap-4">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div>
                    <Skeleton className="h-4 w-32 mb-1" />
                    <Skeleton className="h-3 w-16" />
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Skeleton className="h-6 w-16" />
                  <Skeleton className="h-8 w-8 rounded-full" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="shadow-sm">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-medium">Upcoming Appointments</CardTitle>
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-8 text-xs"
            onClick={() => navigate('/appointments')}
          >
            View All
          </Button>
        </div>
        <CardDescription>
          {appointments.length > 0 ? 
            `Next appointment: ${formatDate(appointments[0].appointment_date)}` : 
            'No upcoming appointments'
          }
        </CardDescription>
      </CardHeader>
      <CardContent className="p-0">
        {appointments.length === 0 ? (
          <div className="flex items-center justify-center p-6">
            <p className="text-sm text-muted-foreground">No upcoming appointments found</p>
          </div>
        ) : (
          <div className="divide-y">
            {appointments.map((appointment) => (
              <div key={appointment.id} className="flex items-center justify-between p-4 hover:bg-muted/50">
                <div className="flex items-center gap-4">
                  <div className="flex flex-col items-center justify-center h-10 w-10 rounded-full bg-primary/10 text-primary">
                    <Calendar className="h-5 w-5" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">{appointment.patient_name}</p>
                    <div className="flex items-center gap-2">
                      <div className="flex items-center text-xs text-muted-foreground">
                        <Clock className="h-3 w-3 mr-1" />
                        {appointment.time}
                      </div>
                      <div className="flex items-center text-xs text-muted-foreground">
                        <User className="h-3 w-3 mr-1" />
                        {appointment.doctor}
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className={getTypeVariant(appointment.type || 'Consultation')}>
                    {appointment.type || 'Consultation'}
                  </Badge>
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="h-8 w-8"
                    onClick={() => navigate(`/appointments/${appointment.id}`)}
                  >
                    <FileEdit className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
