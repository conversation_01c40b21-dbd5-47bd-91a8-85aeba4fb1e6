import { LoadingScreen } from '@/components/ui/loading-screen';
import { useAuth } from "@/hooks/useAuth";
import { ReactNode } from "react";
import { Navigate, useLocation } from "react-router-dom";

/**
 * Check if the user has any existing organizations by querying the database
 * @param userId The user ID to check
 * @returns True if the user has existing organizations, false otherwise
 */
function checkForExistingOrganizations(userId: string): boolean {
  // First check localStorage for cached organization data
  try {
    // Check for the last selected organization
    const lastOrgUser = localStorage.getItem('spritely_last_org_user');
    const lastOrgId = localStorage.getItem('spritely_last_org');

    if (lastOrgUser === userId && lastOrgId) {
      return true;
    }

    // Check for any organization data
    const keys = Object.keys(localStorage);
    const orgKeys = keys.filter(key => key.startsWith('spritely_org_') && key.includes(userId));

    if (orgKeys.length > 0) {
      // User has organization data in localStorage
      return true;
    }

    // Also check for the user_roles key which might indicate existing organizations
    const userRolesKey = `user_roles_${userId}`;
    if (localStorage.getItem(userRolesKey)) {
      return true;
    }
  } catch (e) {
    console.warn('Error checking localStorage for organizations:', e);
  }

  // If no cached data, we'll return false and let the component handle the API call
  return false;
}

type ProtectedRouteProps = {
  children: ReactNode;
  requireOrganization?: boolean;
};

export function ProtectedRoute({ children, requireOrganization = true }: ProtectedRouteProps) {
  const { user, organization, isLoading } = useAuth();
  const location = useLocation();

  if (isLoading) {
    return <LoadingScreen />;
  }

  if (!user) {
    // Redirect to login if not authenticated
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // If organization is required but not found, check if we should go to setup or organization selection
  if (requireOrganization && !organization) {
    // First, check if we're already on the setup or organizations page to avoid redirect loops
    if (location.pathname === '/setup' || location.pathname === '/organizations') {
      // Already on the correct page, don't redirect
      return <>{children}</>;
    }

    // Check if the user has existing organizations by looking at localStorage
    const hasExistingOrgs = checkForExistingOrganizations(user.id);

    if (hasExistingOrgs) {
      // If user has organizations but none selected, go to organization selection
      return <Navigate to="/organizations" replace />;
    } else {
      // If user has no organizations, go to setup to create one
      return <Navigate to="/setup" replace />;
    }
  }

  return <>{children}</>;
}