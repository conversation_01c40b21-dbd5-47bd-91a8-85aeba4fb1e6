import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { KeyBinding, useShortcuts } from '@/lib/shortcuts';
import { cn } from '@/lib/utils';
import { Search } from 'lucide-react';
import { useEffect, useState } from 'react';


interface ActionGroup {
  id: string;
  title: string;
  bindings: KeyBinding[];
}

interface Action {
  id: string;
  title: string;
  description: string;
  handler: () => void;
}

export function KeyboardShortcuts() {
  const {
    getBindings,
    getActions,
    setBinding,
    removeBinding,
    checkConflicts
  } = useShortcuts();

  const [groups, setGroups] = useState<ActionGroup[]>([]);
  const [actions, setActions] = useState<Action[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [editingBinding, setEditingBinding] = useState<string | null>(null);
  const [recordingKeys, setRecordingKeys] = useState(false);
  const [currentKeys, setCurrentKeys] = useState<{
    key: string;
    modifiers: string[];
  } | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Convert record to array of groups
    const bindingsRecord = getBindings();
    const groupedBindings = Object.entries(bindingsRecord).reduce((acc, [id, binding]) => {
      const groupId = id.split('.')[0];
      const group = acc.find(g => g.id === groupId);
      if (group) {
        group.bindings.push(binding);
      } else {
        acc.push({
          id: groupId,
          title: groupId.charAt(0).toUpperCase() + groupId.slice(1),
          bindings: [binding]
        });
      }
      return acc;
    }, [] as ActionGroup[]);
    setGroups(groupedBindings);

    // Convert record to array of actions
    const actionsRecord = getActions();
    const actionsList = Object.entries(actionsRecord).map(([id, binding]) => ({
      id,
      title: binding.description,
      description: binding.description,
      handler: () => {} // Placeholder since we don't have access to handlers
    }));
    setActions(actionsList);
  }, [getBindings, getActions]);

  const filteredGroups = groups.map((group: ActionGroup) => ({
    ...group,
    bindings: group.bindings.filter((binding: KeyBinding) =>
      binding.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      binding.action.toLowerCase().includes(searchQuery.toLowerCase())
    )
  })).filter(group => group.bindings.length > 0);

  useEffect(() => {
    if (recordingKeys) {
      const handleKeyDown = (e: KeyboardEvent) => {
        if (!recordingKeys) return;

        e.preventDefault();
        e.stopPropagation();

        const modifiers: string[] = [];
        if (e.ctrlKey) modifiers.push('ctrl');
        if (e.altKey) modifiers.push('alt');
        if (e.shiftKey) modifiers.push('shift');
        if (e.metaKey) modifiers.push('meta');

        setCurrentKeys({
          key: e.key.toLowerCase(),
          modifiers
        });
      };

      const handleKeyUp = () => {
        if (!recordingKeys || !currentKeys || !editingBinding) return;

        const newBinding: KeyBinding = {
          key: currentKeys.key,
          modifiers: currentKeys.modifiers as ("ctrl" | "alt" | "shift" | "meta")[],
          description: actions.find(a => a.id === editingBinding)?.description || '',
          action: editingBinding
        };

        const conflicts = checkConflicts(newBinding);
        if (conflicts.length > 0) {
          setError(`Conflicts with: ${conflicts.join(', ')}`);
          return;
        }

        try {
          setBinding(editingBinding, newBinding);
          // Convert record to array of groups after setting new binding
          const bindingsRecord = getBindings();
          const groupedBindings = Object.entries(bindingsRecord).reduce((acc, [id, binding]) => {
            const groupId = id.split('.')[0];
            const group = acc.find(g => g.id === groupId);
            if (group) {
              group.bindings.push(binding);
            } else {
              acc.push({
                id: groupId,
                title: groupId.charAt(0).toUpperCase() + groupId.slice(1),
                bindings: [binding]
              });
            }
            return acc;
          }, [] as ActionGroup[]);
          setGroups(groupedBindings);
          setError(null);
        } catch (err) {
          setError((err as Error).message);
        }

        setRecordingKeys(false);
        setCurrentKeys(null);
        setEditingBinding(null);
      };

      window.addEventListener('keydown', handleKeyDown);
      window.addEventListener('keyup', handleKeyUp);
      return () => {
        window.removeEventListener('keydown', handleKeyDown);
        window.removeEventListener('keyup', handleKeyUp);
      };
    }
  }, [
    recordingKeys,
    currentKeys,
    editingBinding,
    actions,
    checkConflicts,
    getBindings,
    setBinding
  ]);

  const formatKeyBinding = (binding: KeyBinding) => {
    const modifiers = binding.modifiers.map(m => {
      switch (m) {
        case 'meta': return '⌘';
        case 'shift': return '⇧';
        case 'alt': return '⌥';
        case 'ctrl': return '⌃';
        default: return m;
      }
    });
    return [...modifiers, binding.key.toUpperCase()].join(' + ');
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search shortcuts..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="all" className="w-full">
        <TabsList>
          <TabsTrigger value="all">All Shortcuts</TabsTrigger>
          <TabsTrigger value="custom">Custom Shortcuts</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          <ScrollArea className="h-[600px] pr-4">
            {filteredGroups.map((group) => (
              <div key={group.id} className="mb-8">
                <h3 className="text-lg font-semibold mb-4">{group.title}</h3>
                <div className="space-y-2">
                  {group.bindings.map((binding) => (
                    <div
                      key={binding.action}
                      className={cn(
                        "flex items-center justify-between p-2 rounded-lg",
                        editingBinding === binding.action && "bg-primary/5"
                      )}
                    >
                      <div>
                        <p className="font-medium">{binding.description}</p>
                        <p className="text-sm text-muted-foreground">{binding.action}</p>
                      </div>
                      <div className="flex items-center space-x-2">
                        {editingBinding === binding.action && recordingKeys ? (
                          <div className="px-3 py-1 rounded border bg-primary/5 animate-pulse">
                            Recording...
                          </div>
                        ) : (
                          <div className="flex items-center space-x-2">
                            <kbd className="px-3 py-1 rounded border bg-muted">
                              {formatKeyBinding(binding)}
                            </kbd>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setEditingBinding(binding.action);
                                setRecordingKeys(true);
                              }}
                            >
                              Edit
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                removeBinding(binding.action);
                                const bindingsRecord = getBindings();
                                const groupedBindings = Object.entries(bindingsRecord).reduce((acc, [id, binding]) => {
                                  const groupId = id.split('.')[0];
                                  const group = acc.find(g => g.id === groupId);
                                  if (group) {
                                    group.bindings.push(binding);
                                  } else {
                                    acc.push({
                                      id: groupId,
                                      title: groupId.charAt(0).toUpperCase() + groupId.slice(1),
                                      bindings: [binding]
                                    });
                                  }
                                  return acc;
                                }, [] as ActionGroup[]);
                                setGroups(groupedBindings);
                              }}
                            >
                              Reset
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </ScrollArea>
        </TabsContent>

        <TabsContent value="custom">
          <div className="space-y-4">
            <p className="text-muted-foreground">
              Custom shortcuts will be saved to your profile and sync across devices.
            </p>
            {/* Custom shortcuts UI - To be implemented */}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}