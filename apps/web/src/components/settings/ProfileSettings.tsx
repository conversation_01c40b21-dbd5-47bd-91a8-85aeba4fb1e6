import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { supabase } from '@/lib/supabase';
import { Database } from '@spritely/supabase-types';
import { Building, Mail, Save, User } from 'lucide-react';
import { useState } from "react";

interface ProfileSettingsProps {
  user: {
    id: string;
    email?: string;
    user_metadata?: {
      first_name?: string;
      last_name?: string;
      [key: string]: string | number | boolean | null | undefined;
    };
  };
  organization?: Database['public']['Tables']['organizations']['Row'] | null;
}

export default function ProfileSettings({ user, organization }: ProfileSettingsProps) {
  // Profile state
  const [firstName, setFirstName] = useState(user?.user_metadata?.first_name || '');
  const [lastName, setLastName] = useState(user?.user_metadata?.last_name || '');
  const [profileUpdating, setProfileUpdating] = useState(false);
  const [profileError, setProfileError] = useState<string | null>(null);
  const [profileSuccess, setProfileSuccess] = useState<string | null>(null);

  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) return;

    setProfileUpdating(true);
    setProfileError(null);
    setProfileSuccess(null);

    try {
      const { error } = await supabase.auth.updateUser({
        data: {
          first_name: firstName,
          last_name: lastName
        }
      });

      if (error) {
        setProfileError(error.message);
        return;
      }

      setProfileSuccess('Profile updated successfully');
    } catch (err) {
      setProfileError('An unexpected error occurred');
      console.error(err);
    } finally {
      setProfileUpdating(false);
    }
  };

  return (
    <div className="space-y-6">
      {profileError && (
        <Alert variant="destructive" className="mb-4">
          <AlertDescription>{profileError}</AlertDescription>
        </Alert>
      )}

      {profileSuccess && (
        <Alert className="mb-4 bg-green-50 border-green-200 text-green-800 dark:bg-green-900 dark:border-green-800 dark:text-green-300">
          <AlertDescription>{profileSuccess}</AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleUpdateProfile}>
        <div>
          <div className="flex items-center gap-2 mb-4">
            <User className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-medium">Personal Information</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email Address</Label>
              <div className="flex">
                <Input
                  id="email"
                  value={user?.email || ''}
                  disabled
                  className="rounded-r-none"
                />
                <Button variant="secondary" size="icon" className="rounded-l-none">
                  <Mail className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="userId">User ID</Label>
              <Input
                id="userId"
                value={user?.id || ''}
                disabled
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="firstName">First Name</Label>
              <Input
                id="firstName"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="lastName">Last Name</Label>
              <Input
                id="lastName"
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
              />
            </div>
          </div>
        </div>

        <Separator className="my-6" />

        <div>
          <div className="flex items-center gap-2 mb-4">
            <Building className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-medium">Organization</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="orgName">Organization Name</Label>
              <Input
                id="orgName"
                value={organization?.name || ''}
                disabled
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="orgId">Organization ID</Label>
              <Input
                id="orgId"
                value={organization?.id || ''}
                disabled
              />
            </div>

            {organization?.type && (
              <div className="space-y-2">
                <Label htmlFor="orgType">Organization Type</Label>
                <Input
                  id="orgType"
                  value={organization.type}
                  disabled
                />
              </div>
            )}

            {organization?.subscription_tier && (
              <div className="space-y-2">
                <Label htmlFor="subscriptionTier">Subscription Tier</Label>
                <Input
                  id="subscriptionTier"
                  value={organization.subscription_tier}
                  disabled
                />
              </div>
            )}
          </div>
        </div>

        <div className="flex justify-end mt-6">
          <Button type="submit" disabled={profileUpdating}>
            <Save className="mr-2 h-4 w-4" />
            {profileUpdating ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </form>
    </div>
  );
}
