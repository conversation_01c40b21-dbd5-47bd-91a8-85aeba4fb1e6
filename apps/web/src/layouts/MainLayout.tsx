import { AuthDebugger } from '@/components/ui/auth-debugger';
import { Toaster } from '@/components/ui/toaster';
import { Outlet } from 'react-router-dom';

export function MainLayout() {
  return (
    <div className="flex flex-col h-full w-full bg-background overflow-hidden no-scrollbar">
      {/* Navigation or Sidebar could go here */}

      {/* Main content area */}
      <main className="flex-1 w-full overflow-auto no-scrollbar">
        <Outlet />
      </main>

      {/* Global UI elements */}
      <Toaster />
      <AuthDebugger />
    </div>
  );
}