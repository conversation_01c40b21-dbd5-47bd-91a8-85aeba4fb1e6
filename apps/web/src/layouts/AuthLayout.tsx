import { Toaster } from '@/components/ui/toaster';
import { motion } from 'framer-motion';
import { Outlet, useNavigate } from 'react-router-dom';

export function AuthLayout() {
  const navigate = useNavigate();

  return (
    <div className="h-full w-full overflow-hidden bg-gradient-to-b from-background to-background/95 text-foreground no-scrollbar">
      {/* Simplified Background with Stable Animation */}
      <div className="absolute inset-0 flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:75px_75px] dark:opacity-100 opacity-30" />
        <div
          className="absolute inset-0 bg-gradient-to-tr from-emerald-500/[0.03] to-blue-600/[0.03] dark:from-emerald-500/10 dark:to-blue-600/10 opacity-80"
        />
        <motion.div
          className="absolute inset-auto w-[800px] h-[800px] bg-gradient-to-tr from-emerald-500/[0.08] to-blue-600/[0.08] dark:from-emerald-500/20 dark:to-blue-600/20 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.5, 0.7, 0.5]
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut"
          }}
        />
      </div>

      {/* Subtle Floating Particles */}
      <div className="absolute inset-0 pointer-events-none">
        {Array.from({ length: 10 }).map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-primary/[0.07] dark:bg-primary/10 rounded-full animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDuration: `${15 + i * 2}s`,
              animationDelay: `${i * 0.5}s`
            }}
          />
        ))}
      </div>

      {/* Content */}
      <div className="relative z-10 flex h-full items-center justify-center p-6 overflow-auto no-scrollbar">
        <div className="w-full max-w-md animate-fadeIn my-auto">
          <motion.div
            initial={{ y: -20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-8"
          >
            <motion.div
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.97 }}
              className="group inline-flex flex-col items-center gap-3 cursor-pointer"
              onClick={() => navigate('/')}
            >
              <div className="relative w-16 h-16">
                <div className="absolute inset-0 bg-gradient-to-tr from-emerald-500 to-blue-600 rounded-lg transform rotate-45 shadow-lg group-hover:shadow-emerald-500/25" />
                <span className="absolute inset-0 flex items-center justify-center text-background font-bold text-4xl">S</span>
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-emerald-500 to-blue-600 bg-clip-text text-transparent">
                  SpritelyEHR
                </h1>
                <p className="text-muted-foreground/90 dark:text-muted-foreground/80 text-sm mt-1">Modern Patient Management</p>
              </div>
            </motion.div>
          </motion.div>

          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Outlet />
          </motion.div>
        </div>
      </div>

      <Toaster />
    </div>
  );
}