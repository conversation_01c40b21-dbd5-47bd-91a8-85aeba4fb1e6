import { KeyBinding } from '@/lib/shortcuts';
import { formatShortcut } from '@/lib/shortcuts/utils';
import React, { useEffect, useRef } from 'react';
import { useCommandPalette } from '../providers/CommandPaletteProvider';

const CommandPalette: React.FC = () => {
  const {
    state: { isOpen, searchQuery, selectedIndex, filteredActions },
    closePalette,
    executeAction,
  } = useCommandPalette();

  const inputRef = useRef<HTMLInputElement>(null);
  const selectedRef = useRef<HTMLDivElement>(null);

  // Focus input when palette opens
  useEffect(() => {
    if (isOpen) {
      inputRef.current?.focus();
    }
  }, [isOpen]);

  // Scroll selected item into view
  useEffect(() => {
    if (selectedRef.current) {
      selectedRef.current.scrollIntoView({
        block: 'nearest',
        behavior: 'smooth',
      });
    }
  }, [selectedIndex]);

  if (!isOpen) return null;

  const handleSearchChange = () => {
    // Update is handled by the provider
  };

  const renderShortcut = (binding: KeyBinding) => {
    if (!binding) return null;
    const formattedShortcut = formatShortcut(binding.key, binding.modifiers);
    return (
      <div className="flex items-center space-x-1 text-sm text-gray-500">
        {formattedShortcut.split('').map((char, i) => (
          <React.Fragment key={i}>
            {char === '+' ? (
              <span>+</span>
            ) : (
              <kbd className="px-2 py-1 bg-gray-100 rounded">{char}</kbd>
            )}
          </React.Fragment>
        ))}
      </div>
    );
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={closePalette}
      />

      {/* Command palette modal */}
      <div className="relative min-h-screen flex items-start justify-center p-4 sm:p-6">
        <div className="bg-white rounded-lg shadow-2xl w-full max-w-2xl overflow-hidden">
          {/* Search input */}
          <div className="border-b border-gray-200">
            <div className="flex items-center px-4 py-3">
              <svg
                className="w-5 h-5 text-gray-400"
                fill="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              <input
                ref={inputRef}
                type="text"
                className="w-full px-4 py-2 text-gray-800 placeholder-gray-400 bg-transparent border-0 focus:ring-0 focus:outline-none"
                placeholder="Search commands..."
                value={searchQuery}
                onChange={handleSearchChange}
              />
              <div className="flex items-center space-x-2">
                {renderShortcut({
                  key: "k",
                  modifiers: ["meta"],
                  description: "Open command palette",
                  action: "open_palette"
                })}
              </div>
            </div>
          </div>

          {/* Results list */}
          <div className="max-h-[60vh] overflow-y-auto">
            {filteredActions.length === 0 ? (
              <div className="px-4 py-8 text-center text-gray-500">
                No commands found
              </div>
            ) : (
              <div className="py-2">
                {filteredActions.map((action, index) => (
                  <div
                    key={action.id}
                    ref={index === selectedIndex ? selectedRef : null}
                    className={`px-4 py-3 flex items-center justify-between cursor-pointer ${
                      index === selectedIndex
                        ? 'bg-blue-50 text-blue-700'
                        : 'hover:bg-gray-50'
                    }`}
                    onClick={() => executeAction(action.id)}
                  >
                    <div className="flex items-center space-x-3">
                      {action.icon && (
                        <div className="w-5 h-5 text-gray-400">{action.icon}</div>
                      )}
                      <div>
                        <div className="font-medium">{action.title}</div>
                        <div className="text-sm text-gray-500">
                          {action.description}
                        </div>
                      </div>
                    </div>
                    {action.shortcut && renderShortcut(action.shortcut)}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 px-4 py-3 text-sm text-gray-500">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <span>↑↓</span>
                <span>to navigate</span>
              </div>
              <div className="flex items-center space-x-2">
                <span>enter</span>
                <span>to select</span>
              </div>
              <div className="flex items-center space-x-2">
                <span>esc</span>
                <span>to close</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CommandPalette;