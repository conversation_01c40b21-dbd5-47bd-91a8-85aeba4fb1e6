import {
  CommandAction,
  CustomBinding,
  Modifier<PERSON>ey,
  ShortcutCombo,
  ShortcutConflict,
  ShortcutManagerConfig
} from '../types/shortcuts';

class ShortcutManager {
  private actions: Map<string, CommandAction>;
  private customBindings: Map<string, ShortcutCombo>;
  private config: Required<ShortcutManagerConfig>;
  private systemShortcuts: Set<string>;

  constructor(config: ShortcutManagerConfig = {}) {
    this.actions = new Map();
    this.customBindings = new Map();
    this.systemShortcuts = new Set();

    // Set default configuration
    this.config = {
      detectSystemConflicts: true,
      persistPreferences: true,
      storageKey: 'ehr-shortcuts',
      platform: this.detectPlatform(),
      ...config
    };

    this.initializeSystemShortcuts();
    this.loadCustomBindings();
    this.setupEventListeners();
  }

  private detectPlatform(): 'mac' | 'windows' | 'linux' {
    const platform = navigator.platform.toLowerCase();
    if (platform.includes('mac')) return 'mac';
    if (platform.includes('win')) return 'windows';
    return 'linux';
  }

  private initializeSystemShortcuts(): void {
    // Common system shortcuts to avoid conflicts
    const systemShortcuts: Array<{ modifiers: ModifierKey[], key: string }> = [
      { modifiers: ['⌘'], key: 'c' }, // Copy
      { modifiers: ['⌘'], key: 'v' }, // Paste
      { modifiers: ['⌘'], key: 'x' }, // Cut
      { modifiers: ['⌘'], key: 'z' }, // Undo
      { modifiers: ['⌘'], key: 'a' }, // Select All
      { modifiers: ['⌘'], key: 'f' }, // Find
      { modifiers: ['⌘'], key: 'p' }, // Print
      { modifiers: ['⌘'], key: 's' }, // Save
      { modifiers: ['⌘'], key: 'w' }, // Close Window
      { modifiers: ['⌘'], key: 'q' }, // Quit
    ];

    systemShortcuts.forEach(shortcut => {
      this.systemShortcuts.add(this.getShortcutKey(shortcut));
    });
  }

  private setupEventListeners(): void {
    window.addEventListener('keydown', this.handleKeyDown.bind(this));
  }

  private handleKeyDown(event: KeyboardEvent): void {
    const shortcutKey = this.getShortcutKeyFromEvent(event);
    const action = this.findActionByShortcut(shortcutKey);

    if (action) {
      event.preventDefault();
      this.executeAction(action.id);
    }
  }

  private getShortcutKeyFromEvent(event: KeyboardEvent): string {
    const modifiers: ModifierKey[] = [];

    if (event.metaKey) modifiers.push('⌘');
    if (event.ctrlKey) modifiers.push('Ctrl');
    if (event.shiftKey) modifiers.push('⇧');
    if (event.altKey) modifiers.push('⌥');

    return this.getShortcutKey({ modifiers, key: event.key.toLowerCase() });
  }

  private getShortcutKey(shortcut: ShortcutCombo): string {
    return `${shortcut.modifiers.sort().join('+')}+${shortcut.key}`;
  }

  private findActionByShortcut(shortcutKey: string): CommandAction | undefined {
    for (const action of this.actions.values()) {
      const customBinding = this.customBindings.get(action.id);
      const shortcut = customBinding || action.shortcut;

      if (shortcut && this.getShortcutKey(shortcut) === shortcutKey) {
        return action;
      }
    }
    return undefined;
  }

  public registerAction(action: CommandAction): void {
    if (this.actions.has(action.id)) {
      throw new Error(`Action with ID ${action.id} is already registered`);
    }
    this.actions.set(action.id, action);
  }

  public unregisterAction(actionId: string): void {
    this.actions.delete(actionId);
    this.customBindings.delete(actionId);
    this.saveCustomBindings();
  }

  public executeAction(actionId: string): void {
    const action = this.actions.get(actionId);
    if (!action) {
      throw new Error(`Action with ID ${actionId} not found`);
    }
    action.handler();
  }

  public setCustomBinding(actionId: string, shortcut: ShortcutCombo): void {
    if (!this.actions.has(actionId)) {
      throw new Error(`Action with ID ${actionId} not found`);
    }

    const shortcutKey = this.getShortcutKey(shortcut);

    // Check for system conflicts
    if (this.config.detectSystemConflicts && this.systemShortcuts.has(shortcutKey)) {
      throw new Error('This shortcut conflicts with a system shortcut');
    }

    this.customBindings.set(actionId, shortcut);
    this.saveCustomBindings();
  }

  public removeCustomBinding(actionId: string): void {
    this.customBindings.delete(actionId);
    this.saveCustomBindings();
  }

  public getConflicts(): ShortcutConflict[] {
    const conflicts: Map<string, CommandAction[]> = new Map();

    // Check all actions and their shortcuts
    for (const action of this.actions.values()) {
      const shortcut = this.customBindings.get(action.id) || action.shortcut;
      if (!shortcut) continue;

      const shortcutKey = this.getShortcutKey(shortcut);
      const conflictingActions = conflicts.get(shortcutKey) || [];
      conflictingActions.push(action);
      conflicts.set(shortcutKey, conflictingActions);
    }

    // Filter and format conflicts
    return Array.from(conflicts.entries())
      .filter(entry => entry[1].length > 1)
      .map(entry => ({
        shortcut: entry[1][0].shortcut!,
        actions: entry[1]
      }));
  }

  private loadCustomBindings(): void {
    if (!this.config.persistPreferences) return;

    try {
      const stored = localStorage.getItem(this.config.storageKey);
      if (stored) {
        const bindings: CustomBinding[] = JSON.parse(stored);
        bindings.forEach(binding => {
          this.customBindings.set(binding.actionId, binding.shortcut);
        });
      }
    } catch (error) {
      console.error('Failed to load custom bindings:', error);
    }
  }

  private saveCustomBindings(): void {
    if (!this.config.persistPreferences) return;

    try {
      const bindings: CustomBinding[] = Array.from(this.customBindings.entries())
        .map(([actionId, shortcut]) => ({ actionId, shortcut }));
      localStorage.setItem(this.config.storageKey, JSON.stringify(bindings));
    } catch (error) {
      console.error('Failed to save custom bindings:', error);
    }
  }

  public getAllActions(): CommandAction[] {
    return Array.from(this.actions.values());
  }

  public getCustomBindings(): Map<string, ShortcutCombo> {
    return new Map(this.customBindings);
  }

  public destroy(): void {
    window.removeEventListener('keydown', this.handleKeyDown.bind(this));
  }
}

export default ShortcutManager;
