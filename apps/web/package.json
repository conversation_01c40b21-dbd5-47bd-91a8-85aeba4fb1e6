{"name": "web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:no-types": "vite", "dev:with-supabase": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "force-errors": "tsx scripts/force-errors.ts", "diagnose": "tsc --extendedDiagnostics --noEmit"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@supabase/supabase-js": "^2.49.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.12.1", "input-otp": "^1.4.2", "lucide-react": "^0.487.0", "next-themes": "^0.4.6", "react": "18.3.1", "react-day-picker": "^8.10.1", "react-dom": "18.3.1", "react-hook-form": "^7.55.0", "react-resizable-panels": "^2.1.7", "react-router-dom": "^7.5.0", "recharts": "^2.15.2", "sonner": "^2.0.3", "@spritely/supabase-types": "*", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5", "vaul": "^1.1.2", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.21.0", "@stagewise/toolbar-react": "^0.1.2", "@types/node": "^22.14.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "postcss": "^8.4.21", "tailwindcss": "^3.3.0", "tailwindcss-animate": "^1.0.7", "tsx": "^4.7.1", "typescript": "^5.8.3", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}