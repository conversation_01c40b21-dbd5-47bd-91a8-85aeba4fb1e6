#!/bin/bash

# Script to properly set up Supabase with schema and seed data
echo "🔄 Setting up Supabase..."

# Stop any running Supabase instances
echo "🛑 Stopping any running Supabase instances..."
supabase stop

# Reset Supabase to ensure a clean state
echo "🧹 Resetting Supabase..."
supabase db reset

# Start Supabase with migrations
echo "🚀 Starting Supabase..."
supabase start

# Wait for Supabase to be ready
echo "⏳ Waiting for Supabase to be ready..."
sleep 15

# Apply schema from migrations
echo "📊 Applying database schema from migrations..."
psql postgresql://postgres:postgres@127.0.0.1:54322/postgres -f supabase/migrations/20240518215410_initial_schema.sql
psql postgresql://postgres:postgres@127.0.0.1:54322/postgres -f supabase/migrations/20240518215411_fix_audit_logs.sql

# Create audit_logs table if it doesn't exist
echo "📝 Ensuring audit_logs table exists..."
psql postgresql://postgres:postgres@127.0.0.1:54322/postgres -c "
CREATE TABLE IF NOT EXISTS public.audit_logs (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    table_name text NOT NULL,
    record_id uuid NOT NULL,
    action text NOT NULL,
    old_data jsonb,
    new_data jsonb,
    changed_by uuid,
    timestamp timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);
ALTER TABLE public.audit_logs OWNER TO postgres;
"

# Apply seed data
echo "🌱 Seeding database..."

# Apply setup files
echo "  - Applying setup files..."
for file in supabase/seeds/common/00_*.sql; do
  echo "    - Applying $file"
  psql postgresql://postgres:postgres@127.0.0.1:54322/postgres -f "$file"
done

# Apply core data
echo "  - Applying core data..."
for file in supabase/seeds/common/01_core/*.sql; do
  echo "    - Applying $file"
  psql postgresql://postgres:postgres@127.0.0.1:54322/postgres -f "$file"
done

# Apply reference data
echo "  - Applying reference data..."
for file in supabase/seeds/common/02_reference/*.sql; do
  echo "    - Applying $file"
  psql postgresql://postgres:postgres@127.0.0.1:54322/postgres -f "$file"
done

# Apply development-specific seed data (skipping problematic files)
echo "  - Applying development seed data..."
for file in supabase/seeds/environments/development/*.sql; do
  # Skip the problematic test_users file
  if [[ "$file" != *"01_test_users.sql" ]]; then
    echo "    - Applying $file"
    psql postgresql://postgres:postgres@127.0.0.1:54322/postgres -f "$file"
  else
    echo "    - Skipping $file (known issue with auth.users)"
  fi
done

# Create test users directly
echo "  - Creating test users directly..."
psql postgresql://postgres:postgres@127.0.0.1:54322/postgres -c "
-- Create admin user
INSERT INTO auth.users (id, email, encrypted_password, email_confirmed_at, raw_app_meta_data, raw_user_meta_data, created_at, updated_at)
VALUES
  (gen_random_uuid(), '<EMAIL>', '\$2a\$10\$Ql9XZz3Jz8KPpJbVrFVzXOQCKLNRhJT9QV0OfJYK/CExqhxGvuwRW', NOW(),
   '{\"provider\":\"email\",\"providers\":[\"email\"]}',
   '{\"name\":\"Admin User\",\"is_test\":true}',
   NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Create provider user
INSERT INTO auth.users (id, email, encrypted_password, email_confirmed_at, raw_app_meta_data, raw_user_meta_data, created_at, updated_at)
VALUES
  (gen_random_uuid(), '<EMAIL>', '\$2a\$10\$Ql9XZz3Jz8KPpJbVrFVzXOQCKLNRhJT9QV0OfJYK/CExqhxGvuwRW', NOW(),
   '{\"provider\":\"email\",\"providers\":[\"email\"]}',
   '{\"name\":\"Provider User\",\"is_test\":true}',
   NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Create nurse user
INSERT INTO auth.users (id, email, encrypted_password, email_confirmed_at, raw_app_meta_data, raw_user_meta_data, created_at, updated_at)
VALUES
  (gen_random_uuid(), '<EMAIL>', '\$2a\$10\$Ql9XZz3Jz8KPpJbVrFVzXOQCKLNRhJT9QV0OfJYK/CExqhxGvuwRW', NOW(),
   '{\"provider\":\"email\",\"providers\":[\"email\"]}',
   '{\"name\":\"Nurse User\",\"is_test\":true}',
   NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Create staff user
INSERT INTO auth.users (id, email, encrypted_password, email_confirmed_at, raw_app_meta_data, raw_user_meta_data, created_at, updated_at)
VALUES
  (gen_random_uuid(), '<EMAIL>', '\$2a\$10\$Ql9XZz3Jz8KPpJbVrFVzXOQCKLNRhJT9QV0OfJYK/CExqhxGvuwRW', NOW(),
   '{\"provider\":\"email\",\"providers\":[\"email\"]}',
   '{\"name\":\"Front Desk User\",\"is_test\":true}',
   NOW(), NOW())
ON CONFLICT (id) DO NOTHING;
"

# Create identities for the test users
echo "  - Creating identities for test users..."
psql postgresql://postgres:postgres@127.0.0.1:54322/postgres -c "
INSERT INTO auth.identities (id, user_id, identity_data, provider, last_sign_in_at, created_at, updated_at)
SELECT
  gen_random_uuid(),
  id,
  jsonb_build_object('sub', id::text, 'email', email),
  'email',
  NOW(),
  NOW(),
  NOW()
FROM auth.users
WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>')
AND NOT EXISTS (
  SELECT 1 FROM auth.identities i WHERE i.user_id = auth.users.id
);
"

# Create user roles for the test users
echo "  - Creating user roles for test users..."
psql postgresql://postgres:postgres@127.0.0.1:54322/postgres -c "
-- First, clean up duplicate user roles
DELETE FROM public.user_roles;

-- Get the first organization ID
WITH org AS (
  SELECT id FROM public.organizations ORDER BY id LIMIT 1
)
-- Create user roles
INSERT INTO public.user_roles (id, user_id, organization_id, role, created_at, updated_at)
SELECT
  gen_random_uuid(),
  u.id,
  o.id,
  CASE
    WHEN u.email = '<EMAIL>' THEN 'system_admin'
    WHEN u.email = '<EMAIL>' THEN 'physician'
    WHEN u.email = '<EMAIL>' THEN 'physician'
    WHEN u.email = '<EMAIL>' THEN 'registered_nurse'
    WHEN u.email = '<EMAIL>' THEN 'registered_nurse'
    WHEN u.email = '<EMAIL>' THEN 'front_desk'
    WHEN u.email = '<EMAIL>' THEN 'billing_staff'
  END,
  NOW(),
  NOW()
FROM auth.users u, org o
WHERE u.email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>',
                 '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>');
"

# Generate sample data for patients and providers
echo "  - Generating sample data for patients and providers..."
psql postgresql://postgres:postgres@127.0.0.1:54322/postgres -c "
-- Create sample healthcare providers
WITH org AS (
  SELECT id FROM public.organizations ORDER BY id LIMIT 1
),
dept AS (
  SELECT id FROM public.departments ORDER BY id LIMIT 1
),
users AS (
  SELECT id, email FROM auth.users WHERE email IN ('<EMAIL>', '<EMAIL>')
)
INSERT INTO public.healthcare_providers (
  id, user_id, first_name, last_name, provider_type, specialization,
  organization_id, department_id, role, created_at, updated_at
)
SELECT
  gen_random_uuid(),
  u.id,
  CASE
    WHEN u.email = '<EMAIL>' THEN 'John'
    WHEN u.email = '<EMAIL>' THEN 'Sarah'
  END,
  CASE
    WHEN u.email = '<EMAIL>' THEN 'Smith'
    WHEN u.email = '<EMAIL>' THEN 'Johnson'
  END,
  CASE
    WHEN u.email = '<EMAIL>' THEN 'physician'
    WHEN u.email = '<EMAIL>' THEN 'nurse'
  END,
  CASE
    WHEN u.email = '<EMAIL>' THEN 'Family Medicine'
    WHEN u.email = '<EMAIL>' THEN 'General Care'
  END,
  o.id,
  d.id,
  CASE
    WHEN u.email = '<EMAIL>' THEN 'physician'
    WHEN u.email = '<EMAIL>' THEN 'registered_nurse'
  END,
  NOW(),
  NOW()
FROM users u, org o, dept d
WHERE NOT EXISTS (
  SELECT 1 FROM public.healthcare_providers p WHERE p.user_id = u.id
);

-- Create sample patients
WITH org AS (
  SELECT id FROM public.organizations ORDER BY id LIMIT 1
)
INSERT INTO public.patients (
  id, first_name, last_name, date_of_birth, gender, phone, email,
  organization_id, created_at, updated_at
)
SELECT
  gen_random_uuid(),
  first_name,
  last_name,
  date_of_birth::date,
  gender,
  phone,
  email,
  o.id,
  NOW(),
  NOW()
FROM (
  VALUES
    ('Michael', 'Johnson', '1985-03-15', 'male', '************', '<EMAIL>'),
    ('Emily', 'Williams', '1990-07-22', 'female', '************', '<EMAIL>'),
    ('David', 'Brown', '1978-11-30', 'male', '************', '<EMAIL>'),
    ('Jessica', 'Davis', '1982-05-10', 'female', '************', '<EMAIL>'),
    ('Robert', 'Miller', '1965-09-18', 'male', '************', '<EMAIL>')
) AS sample_patients(first_name, last_name, date_of_birth, gender, phone, email), org o
WHERE NOT EXISTS (
  SELECT 1 FROM public.patients p WHERE p.email = sample_patients.email
)
LIMIT 5;
"

echo "✅ Supabase setup complete!"
echo "🔑 You can now log in with the following test users:"
echo "   - <EMAIL> / password123 (Admin)"
echo "   - <EMAIL> / password123 (Provider)"
echo "   - <EMAIL> / password123 (Nurse)"
echo "   - <EMAIL> / password123 (Staff)"
