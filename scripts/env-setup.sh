#!/bin/bash

# Unified environment setup script for Spritely
# Works for both local development and CI/CD

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to display usage
usage() {
  echo -e "${BLUE}Usage:${NC} $0 [local|development|staging] [--ci]"
  echo ""
  echo "Set up environment configuration for different deployment environments."
  echo "This script uses 1Password CLI to securely retrieve credentials."
  echo ""
  echo -e "${BLUE}Options:${NC}"
  echo "  local        Configure for local Supabase development"
  echo "  development  Configure for development environment (dev branch)"
  echo "  staging      Configure for staging environment (staging branch)"
  echo "  --ci         Run in CI mode (requires OP_SERVICE_ACCOUNT_TOKEN environment variable)"
  echo ""
  echo -e "${BLUE}Requirements:${NC}"
  echo "  - 1Password CLI (op) must be installed"
  echo "  - For CI mode: OP_SERVICE_ACCOUNT_TOKEN environment variable must be set"
  echo "  - For interactive mode: User must be authenticated with 1Password CLI"
  echo ""
  exit 1
}

# Check if 1Password CLI is installed
check_op_cli() {
  if ! command -v op &> /dev/null; then
    echo -e "${RED}Error:${NC} 1Password CLI is not installed."
    echo "Please install it from https://1password.com/downloads/command-line/"
    exit 1
  fi
}

# Get the project root directory
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
WEB_ENV_FILE="$PROJECT_ROOT/apps/web/.env"
MOBILE_ENV_FILE="$PROJECT_ROOT/apps/mobile/.env"
CI_ENV_FILE="$PROJECT_ROOT/.env.ci"

# Check if arguments are provided
if [ $# -lt 1 ]; then
  usage
fi

# Parse arguments
ENV="$1"
CI_MODE=false

if [ "$2" == "--ci" ]; then
  CI_MODE=true
fi

# Validate environment argument
if [[ ! "$ENV" =~ ^(local|development|staging)$ ]]; then
  echo -e "${RED}Error:${NC} Invalid environment: $ENV"
  usage
fi

# Function to sign in to 1Password in CI mode
op_signin_ci() {
  if [ -z "$OP_SERVICE_ACCOUNT_TOKEN" ]; then
    echo -e "${RED}Error:${NC} OP_SERVICE_ACCOUNT_TOKEN environment variable is not set."
    echo "This is required for CI mode."
    exit 1
  fi

  echo -e "${BLUE}Using 1Password service account token...${NC}"
  # The token is already in the environment variable, so we don't need to do anything
  # 1Password CLI will automatically use it
}

# Function to check if user is signed in to 1Password in interactive mode
op_check_signin_interactive() {
  if ! op account get &> /dev/null; then
    echo -e "${YELLOW}Warning:${NC} Not authenticated with 1Password CLI."
    echo "Please run 'op signin' first."
    exit 1
  fi
}

# Function to create environment file
create_env_file() {
  local file="$1"
  local url="$2"
  local key="$3"
  local env_name="$4"

  echo "# Generated by env-setup.sh script" > "$file"
  echo "# Environment: $env_name" >> "$file"
  echo "# Generated on: $(date)" >> "$file"
  echo "" >> "$file"

  if [[ "$file" == *"/web/"* ]]; then
    echo "VITE_SUPABASE_URL=$url" >> "$file"
    echo "VITE_SUPABASE_ANON_KEY=$key" >> "$file"
    echo "VITE_APP_ENV=$env_name" >> "$file"
  elif [[ "$file" == *"/mobile/"* ]]; then
    echo "EXPO_PUBLIC_SUPABASE_URL=$url" >> "$file"
    echo "EXPO_PUBLIC_SUPABASE_ANON_KEY=$key" >> "$file"
    echo "EXPO_PUBLIC_APP_ENV=$env_name" >> "$file"
  elif [[ "$file" == *".env.ci"* ]]; then
    echo "SUPABASE_PROJECT_ID=$env_name" >> "$file"
  fi

  echo -e "${GREEN}Created${NC} environment file: $file"
}

# Function to get item from 1Password
get_op_item() {
  local item_name="Spritely Supabase $ENV"
  local field="$1"
  local vault="Spritely"

  # Get the field value
  local value
  value=$(op item get "$item_name" --vault "$vault" --field "$field" 2>/dev/null)

  if [ -z "$value" ]; then
    echo -e "${RED}Error:${NC} Failed to retrieve '$field' from 1Password item '$item_name'."
    echo "Please make sure the item '$item_name' exists in vault '$vault' with field '$field'."
    echo "You can run 'npm run 1password:setup' to set up the vault structure."
    exit 1
  fi

  echo "$value"
}

# Main execution
echo -e "${BLUE}Setting up $ENV environment...${NC}"

# Handle local environment separately (no 1Password needed)
if [ "$ENV" == "local" ]; then
  echo -e "${BLUE}Setting up local development environment...${NC}"

  # Local Supabase configuration
  SUPABASE_URL="http://127.0.0.1:54321"
  SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0"

  create_env_file "$WEB_ENV_FILE" "$SUPABASE_URL" "$SUPABASE_ANON_KEY" "local"
  create_env_file "$MOBILE_ENV_FILE" "$SUPABASE_URL" "$SUPABASE_ANON_KEY" "local"

  echo -e "${YELLOW}Note:${NC} Make sure your local Supabase instance is running with 'npm run supabase:setup'"
else
  # Check 1Password CLI
  check_op_cli

  # Sign in to 1Password if in CI mode, or check if signed in if in interactive mode
  if [ "$CI_MODE" = true ]; then
    op_signin_ci
  else
    op_check_signin_interactive
  fi

  # Retrieve credentials from 1Password
  echo -e "${BLUE}Retrieving credentials from 1Password...${NC}"

  # Get Supabase URL and anon key from 1Password
  SUPABASE_URL=$(get_op_item "url")
  SUPABASE_ANON_KEY=$(get_op_item "anon_key")
  SUPABASE_PROJECT_ID=$(get_op_item "project_id")

  # Create environment files
  create_env_file "$WEB_ENV_FILE" "$SUPABASE_URL" "$SUPABASE_ANON_KEY" "$ENV"
  create_env_file "$MOBILE_ENV_FILE" "$SUPABASE_URL" "$SUPABASE_ANON_KEY" "$ENV"

  # Create CI environment file if in CI mode
  if [ "$CI_MODE" = true ]; then
    create_env_file "$CI_ENV_FILE" "" "" "$SUPABASE_PROJECT_ID"
  fi
fi

echo -e "${GREEN}Done!${NC} Environment configured for $ENV."
echo ""
echo -e "${YELLOW}Security Note:${NC} The environment files contain sensitive information."
echo "Do not commit them to version control or share them publicly."
