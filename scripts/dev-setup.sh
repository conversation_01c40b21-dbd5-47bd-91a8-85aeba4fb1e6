#!/bin/bash

# Developer onboarding script for Spritely
# This script guides new developers through the setup process

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Get the project root directory
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# Function to check if a command exists
command_exists() {
  command -v "$1" &> /dev/null
}

# Function to check if 1Password CLI is installed
check_op_cli() {
  if ! command_exists op; then
    echo -e "${YELLOW}1Password CLI not found.${NC}"

    # Detect OS
    if [[ "$OSTYPE" == "darwin"* ]]; then
      echo -e "${BLUE}Installing 1Password CLI via Homebrew...${NC}"
      if ! command_exists brew; then
        echo -e "${RED}Error: Homebrew is not installed.${NC}"
        echo "Please install Homebrew first: https://brew.sh/"
        exit 1
      fi
      brew install --cask 1password-cli
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
      echo -e "${YELLOW}Please install 1Password CLI manually:${NC}"
      echo "https://1password.com/downloads/command-line/"
      exit 1
    else
      echo -e "${YELLOW}Please install 1Password CLI manually:${NC}"
      echo "https://1password.com/downloads/command-line/"
      exit 1
    fi
  fi

  echo -e "${GREEN}1Password CLI is installed.${NC}"
}

# Function to check if user is signed in to 1Password
check_op_signin() {
  if ! op account get &> /dev/null; then
    echo -e "${YELLOW}You need to sign in to 1Password CLI.${NC}"
    echo -e "${BLUE}Running 'op signin'...${NC}"
    op signin

    # Check if signin was successful
    if ! op account get &> /dev/null; then
      echo -e "${RED}Failed to sign in to 1Password CLI.${NC}"
      echo "Please make sure you have access to the Spritely vault."
      exit 1
    fi
  fi

  echo -e "${GREEN}Successfully signed in to 1Password CLI.${NC}"
}

# Function to check if Supabase CLI is installed
check_supabase_cli() {
  if ! command_exists supabase; then
    echo -e "${YELLOW}Supabase CLI not found.${NC}"
    echo -e "${BLUE}Installing Supabase CLI...${NC}"

    if command_exists npm; then
      npm install -g supabase
    else
      echo -e "${RED}Error: npm is not installed.${NC}"
      echo "Please install Node.js and npm first."
      exit 1
    fi
  fi

  echo -e "${GREEN}Supabase CLI is installed.${NC}"
}

# Function to check if the Spritely vault exists
check_vault_exists() {
  if ! op vault get Spritely &> /dev/null; then
    echo -e "${YELLOW}The 'Spritely' vault does not exist or you don't have access to it.${NC}"
    echo -e "${BLUE}Running 1Password vault setup...${NC}"
    ./scripts/setup-1password-vault.sh
  fi

  echo -e "${GREEN}You have access to the Spritely vault.${NC}"
}

# Function to check if environment items exist
check_environment_items() {
  local environments=("local" "development" "staging")
  local missing=false

  for env in "${environments[@]}"; do
    if ! op item get "Spritely Supabase $env" --vault Spritely &> /dev/null; then
      echo -e "${YELLOW}The 'Spritely Supabase $env' item does not exist in the Spritely vault.${NC}"
      missing=true
    fi
  done

  if [ "$missing" = true ]; then
    echo -e "${BLUE}Running 1Password organization script...${NC}"
    ./scripts/organize-1password.sh
  else
    echo -e "${GREEN}All required 1Password items exist.${NC}"
  fi
}

# Main function
main() {
  echo -e "${BLUE}=== Spritely Developer Setup ===${NC}"
  echo "This script will guide you through the setup process for Spritely development."
  echo ""

  # Check prerequisites
  echo -e "${BLUE}Checking prerequisites...${NC}"
  check_op_cli
  check_op_signin
  check_supabase_cli
  check_vault_exists
  check_environment_items

  # Set up local environment
  echo -e "\n${BLUE}Setting up local development environment...${NC}"
  npm run env:local

  # Set up Supabase
  echo -e "\n${BLUE}Setting up Supabase...${NC}"
  npm run supabase:setup

  # Generate types
  echo -e "\n${BLUE}Generating TypeScript types...${NC}"
  npm run generate-types

  echo -e "\n${GREEN}Setup complete!${NC}"
  echo -e "You can now start the development server with: ${YELLOW}npm run dev:full${NC}"
  echo ""
  echo -e "${BLUE}Available environments:${NC}"
  echo "- Local: npm run env:local (default)"
  echo "- Development: npm run env:development (dev branch)"
  echo "- Staging: npm run env:staging (staging branch)"
  echo ""
  echo -e "${YELLOW}Note:${NC} After changing environments, restart the development server."
}

# Run the main function
main
