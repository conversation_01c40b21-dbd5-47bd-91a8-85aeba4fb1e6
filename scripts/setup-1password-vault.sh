#!/bin/bash

# Script to set up 1Password vault for Spritely Supabase credentials
# This script creates a dedicated vault and items for each environment

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to display usage
usage() {
  echo -e "${BLUE}Usage:${NC} $0"
  echo ""
  echo "Set up a 1Password vault for Spritely Supabase credentials."
  echo "This script will create a dedicated vault and items for each environment."
  echo ""
  echo -e "${BLUE}Requirements:${NC}"
  echo "  - 1Password CLI (op) must be installed and authenticated"
  echo "  - You must have permission to create vaults in your 1Password account"
  echo ""
  exit 1
}

# Check if 1Password CLI is installed
check_op_cli() {
  if ! command -v op &> /dev/null; then
    echo -e "${RED}Error:${NC} 1Password CLI is not installed."
    echo "Please install it from https://1password.com/downloads/command-line/"
    exit 1
  fi

  # Check if authenticated
  if ! op account get &> /dev/null; then
    echo -e "${YELLOW}Warning:${NC} Not authenticated with 1Password CLI."
    echo "Please run 'op signin' first."
    exit 1
  fi
}

# Function to create a vault if it doesn't exist
create_vault() {
  local vault_name="$1"

  # Check if vault exists
  if op vault get "$vault_name" &> /dev/null; then
    echo -e "${YELLOW}Vault '${vault_name}' already exists.${NC}"
    return 0
  fi

  # Create vault
  echo -e "${BLUE}Creating vault '${vault_name}'...${NC}"
  op vault create "$vault_name" --description "Spritely Supabase credentials for all environments"

  if [ $? -eq 0 ]; then
    echo -e "${GREEN}Vault '${vault_name}' created successfully.${NC}"
  else
    echo -e "${RED}Failed to create vault '${vault_name}'.${NC}"
    exit 1
  fi
}

# Function to create an item for an environment
create_environment_item() {
  local vault_name="$1"
  local env_name="$2"
  local item_name="Spritely Supabase $env_name"

  # Check if item exists
  if op item get "$item_name" --vault "$vault_name" &> /dev/null; then
    echo -e "${YELLOW}Item '${item_name}' already exists in vault '${vault_name}'.${NC}"
    return 0
  fi

  # Create item
  echo -e "${BLUE}Creating item '${item_name}' in vault '${vault_name}'...${NC}"

  # Create a template for the item
  cat > /tmp/supabase_item_template.json << EOL
{
  "title": "${item_name}",
  "category": "API_CREDENTIAL",
  "fields": [
    {
      "id": "notesPlain",
      "type": "STRING",
      "purpose": "NOTES",
      "label": "notesPlain",
      "value": "Supabase credentials for ${env_name} environment"
    },
    {
      "id": "url",
      "type": "STRING",
      "label": "URL",
      "value": ""
    },
    {
      "id": "project_id",
      "type": "STRING",
      "label": "Project ID",
      "value": ""
    },
    {
      "id": "anon_key",
      "type": "CONCEALED",
      "label": "Anon Key",
      "value": ""
    },
    {
      "id": "service_key",
      "type": "CONCEALED",
      "label": "Service Key",
      "value": ""
    }
  ]
}
EOL

  # Create the item
  op item create --template /tmp/supabase_item_template.json --vault "$vault_name"

  if [ $? -eq 0 ]; then
    echo -e "${GREEN}Item '${item_name}' created successfully in vault '${vault_name}'.${NC}"
  else
    echo -e "${RED}Failed to create item '${item_name}' in vault '${vault_name}'.${NC}"
    exit 1
  fi

  # Clean up
  rm /tmp/supabase_item_template.json
}

# Main function
main() {
  # Check 1Password CLI
  check_op_cli

  # Create vault
  VAULT_NAME="Spritely"
  create_vault "$VAULT_NAME"

  # Create items for each environment
  create_environment_item "$VAULT_NAME" "local"
  create_environment_item "$VAULT_NAME" "development"
  create_environment_item "$VAULT_NAME" "staging"
  # Note: We don't set up production environment for developers
  # Production will use real data, not seeded test data

  echo -e "\n${GREEN}1Password vault setup complete!${NC}"
  echo -e "${YELLOW}Next steps:${NC}"
  echo "1. Open 1Password and navigate to the '$VAULT_NAME' vault"
  echo "2. Edit each environment item and add the actual Supabase credentials"
  echo "3. Run './scripts/env-setup.sh [environment]' to configure your local environment"
  echo ""
}

# Run the main function
main
