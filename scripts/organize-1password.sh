#!/bin/bash

# <PERSON>ript to organize 1Password vault for Spritely
# This script updates existing items with better descriptions and tags

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to check if 1Password CLI is installed
check_op_cli() {
  if ! command -v op &> /dev/null; then
    echo -e "${RED}Error:${NC} 1Password CLI is not installed."
    echo "Please install it from https://1password.com/downloads/command-line/"
    exit 1
  fi

  # Check if authenticated
  if ! op account get &> /dev/null; then
    echo -e "${YELLOW}Warning:${NC} Not authenticated with 1Password CLI."
    echo "Please run 'op signin' first."
    exit 1
  fi
}

# Function to check if the Spritely vault exists
check_vault_exists() {
  if ! op vault get Spritely &> /dev/null; then
    echo -e "${RED}Error:${NC} The 'Spritely' vault does not exist."
    echo "Please run 'npm run 1password:setup' first."
    exit 1
  fi
}

# Function to update a Supabase environment item
update_supabase_item() {
  local env="$1"
  local title="Spritely Supabase $env"
  local description="$2"
  local url_value="$3"
  local project_id_value="$4"
  local anon_key_value="$5"
  local service_key_value="$6"

  echo -e "${BLUE}Updating $title...${NC}"

  # Create a template for the updated item
  cat > /tmp/supabase_item_template.json << EOL
{
  "title": "$title",
  "category": "API_CREDENTIAL",
  "tags": ["supabase", "spritely", "$env"],
  "fields": [
    {
      "id": "notesPlain",
      "type": "STRING",
      "purpose": "NOTES",
      "label": "notesPlain",
      "value": "$description"
    },
    {
      "id": "url",
      "type": "STRING",
      "label": "URL",
      "value": "$url_value"
    },
    {
      "id": "project_id",
      "type": "STRING",
      "label": "Project ID",
      "value": "$project_id_value"
    },
    {
      "id": "anon_key",
      "type": "CONCEALED",
      "label": "Anon Key",
      "value": "$anon_key_value"
    },
    {
      "id": "service_key",
      "type": "CONCEALED",
      "label": "Service Key",
      "value": "$service_key_value"
    }
  ]
}
EOL

  # Update the item
  op item edit "$title" --vault Spritely --template /tmp/supabase_item_template.json

  # Clean up
  rm /tmp/supabase_item_template.json
}

# Function to create a Netlify item
create_netlify_item() {
  local env="$1"
  local title="Spritely Netlify $env"
  local description="$2"
  local site_id_value="$3"
  local auth_token_value="$4"

  echo -e "${BLUE}Creating $title...${NC}"

  # Create a template for the item
  cat > /tmp/netlify_item_template.json << EOL
{
  "title": "$title",
  "category": "API_CREDENTIAL",
  "tags": ["netlify", "spritely", "$env"],
  "fields": [
    {
      "id": "notesPlain",
      "type": "STRING",
      "purpose": "NOTES",
      "label": "notesPlain",
      "value": "$description"
    },
    {
      "id": "site_id",
      "type": "STRING",
      "label": "Site ID",
      "value": "$site_id_value"
    },
    {
      "id": "auth_token",
      "type": "CONCEALED",
      "label": "Auth Token",
      "value": "$auth_token_value"
    }
  ]
}
EOL

  # Create the item
  op item create --template /tmp/netlify_item_template.json --vault Spritely

  # Clean up
  rm /tmp/netlify_item_template.json
}

# Function to create a GitHub item
create_github_item() {
  local title="Spritely GitHub"
  local description="GitHub credentials for Spritely repository"
  local repo_url_value="$1"
  local access_token_value="$2"

  echo -e "${BLUE}Creating $title...${NC}"

  # Create a template for the item
  cat > /tmp/github_item_template.json << EOL
{
  "title": "$title",
  "category": "API_CREDENTIAL",
  "tags": ["github", "spritely"],
  "fields": [
    {
      "id": "notesPlain",
      "type": "STRING",
      "purpose": "NOTES",
      "label": "notesPlain",
      "value": "$description"
    },
    {
      "id": "repo_url",
      "type": "STRING",
      "label": "Repository URL",
      "value": "$repo_url_value"
    },
    {
      "id": "access_token",
      "type": "CONCEALED",
      "label": "Access Token",
      "value": "$access_token_value"
    }
  ]
}
EOL

  # Create the item
  op item create --template /tmp/github_item_template.json --vault Spritely

  # Clean up
  rm /tmp/github_item_template.json
}

# Function to create a 1Password service account item
create_1password_service_account() {
  local title="Spritely 1Password Service Account"
  local description="Service account for CI/CD integration with 1Password"
  local token_value="$1"

  echo -e "${BLUE}Creating $title...${NC}"

  # Create a template for the item
  cat > /tmp/1password_item_template.json << EOL
{
  "title": "$title",
  "category": "API_CREDENTIAL",
  "tags": ["1password", "spritely", "ci-cd"],
  "fields": [
    {
      "id": "notesPlain",
      "type": "STRING",
      "purpose": "NOTES",
      "label": "notesPlain",
      "value": "$description"
    },
    {
      "id": "service_account_token",
      "type": "CONCEALED",
      "label": "Service Account Token",
      "value": "$token_value"
    }
  ]
}
EOL

  # Create the item
  op item create --template /tmp/1password_item_template.json --vault Spritely

  # Clean up
  rm /tmp/1password_item_template.json
}

# Main function
main() {
  echo -e "${BLUE}=== Organizing 1Password Vault for Spritely ===${NC}"

  # Check prerequisites
  check_op_cli
  check_vault_exists

  # Prompt for updating Supabase items
  echo -e "\n${YELLOW}Do you want to update the Supabase credential items? (y/n)${NC}"
  read -r update_supabase

  if [[ "$update_supabase" =~ ^[Yy]$ ]]; then
    # Local environment
    echo -e "\n${BLUE}Enter details for local Supabase environment:${NC}"
    echo -e "${YELLOW}URL:${NC} http://127.0.0.1:54321"
    local_url="http://127.0.0.1:54321"
    echo -e "${YELLOW}Project ID:${NC} local"
    local_project_id="local"
    echo -e "${YELLOW}Anon Key:${NC} eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0"
    local_anon_key="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0"
    echo -e "${YELLOW}Service Key:${NC} eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU"
    local_service_key="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU"

    update_supabase_item "local" "Local Supabase instance for development. This is the default configuration for local development." "$local_url" "$local_project_id" "$local_anon_key" "$local_service_key"

    # Development environment
    echo -e "\n${BLUE}Enter details for development Supabase environment:${NC}"
    echo -e "${YELLOW}URL (e.g., https://[project-id].supabase.co):${NC}"
    read -r dev_url
    echo -e "${YELLOW}Project ID:${NC} clwxzkbihuzjdhkjjubx"
    dev_project_id="clwxzkbihuzjdhkjjubx"
    echo -e "${YELLOW}Anon Key:${NC}"
    read -r dev_anon_key
    echo -e "${YELLOW}Service Key:${NC}"
    read -r dev_service_key

    update_supabase_item "development" "Development Supabase instance. Connected to the 'dev' branch in GitHub." "$dev_url" "$dev_project_id" "$dev_anon_key" "$dev_service_key"

    # Staging environment
    echo -e "\n${BLUE}Enter details for staging Supabase environment:${NC}"
    echo -e "${YELLOW}URL (e.g., https://[project-id].supabase.co):${NC}"
    read -r staging_url
    echo -e "${YELLOW}Project ID:${NC} vxzubpcxlkdwqphshimb"
    staging_project_id="vxzubpcxlkdwqphshimb"
    echo -e "${YELLOW}Anon Key:${NC}"
    read -r staging_anon_key
    echo -e "${YELLOW}Service Key:${NC}"
    read -r staging_service_key

    update_supabase_item "staging" "Staging Supabase instance. Connected to the 'staging' branch in GitHub." "$staging_url" "$staging_project_id" "$staging_anon_key" "$staging_service_key"

    # Note: We don't set up production environment for developers
    # Production will use real data, not seeded test data
  fi

  # Prompt for creating Netlify items
  echo -e "\n${YELLOW}Do you want to create Netlify credential items? (y/n)${NC}"
  read -r create_netlify

  if [[ "$create_netlify" =~ ^[Yy]$ ]]; then
    # Development environment
    echo -e "\n${BLUE}Enter details for development Netlify environment:${NC}"
    echo -e "${YELLOW}Site ID:${NC}"
    read -r dev_site_id
    echo -e "${YELLOW}Auth Token:${NC}"
    read -r dev_auth_token

    create_netlify_item "development" "Development Netlify site. Connected to the 'dev' branch in GitHub." "$dev_site_id" "$dev_auth_token"

    # Staging environment
    echo -e "\n${BLUE}Enter details for staging Netlify environment:${NC}"
    echo -e "${YELLOW}Site ID:${NC}"
    read -r staging_site_id
    echo -e "${YELLOW}Auth Token:${NC}"
    read -r staging_auth_token

    create_netlify_item "staging" "Staging Netlify site. Connected to the 'staging' branch in GitHub." "$staging_site_id" "$staging_auth_token"

    # Note: We don't set up production environment for developers
    # Production deployment will be handled separately
  fi

  # Prompt for creating GitHub item
  echo -e "\n${YELLOW}Do you want to create a GitHub credential item? (y/n)${NC}"
  read -r create_github

  if [[ "$create_github" =~ ^[Yy]$ ]]; then
    echo -e "\n${BLUE}Enter details for GitHub:${NC}"
    echo -e "${YELLOW}Repository URL:${NC}"
    read -r repo_url
    echo -e "${YELLOW}Access Token:${NC}"
    read -r access_token

    create_github_item "$repo_url" "$access_token"
  fi

  # Prompt for creating 1Password service account item
  echo -e "\n${YELLOW}Do you want to create a 1Password service account item? (y/n)${NC}"
  read -r create_1password

  if [[ "$create_1password" =~ ^[Yy]$ ]]; then
    echo -e "\n${BLUE}Enter details for 1Password service account:${NC}"
    echo -e "${YELLOW}Service Account Token:${NC}"
    read -r service_account_token

    create_1password_service_account "$service_account_token"
  fi

  echo -e "\n${GREEN}1Password vault organization complete!${NC}"
  echo "Your Spritely vault now contains properly organized and tagged items."
}

# Run the main function
main
