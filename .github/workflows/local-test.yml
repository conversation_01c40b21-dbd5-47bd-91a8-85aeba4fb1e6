name: Local Test

on: 
  workflow_dispatch:

jobs:
  deploy-database:
    name: Deploy Database
    runs-on: ubuntu-latest
    env:
      SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: |
          npm install -g npm@latest
          npm ci

      - name: Install Supabase CLI
        uses: supabase/setup-cli@v1
        with:
          version: latest

      - name: Link Supabase project
        run: |
          source .env.ci
          supabase link --project-ref $SUPABASE_PROJECT_ID

      - name: Push database changes
        run: npm run supabase:push

      - name: Generate TypeScript types
        run: npm run generate-types:development 