name: Deploy

on:
  push:
    branches:
      - dev
      - staging
      - master
  workflow_dispatch:

jobs:
  setup:
    name: Setup Environment
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.set-env.outputs.environment }}
      deploy_environment: ${{ steps.set-env.outputs.deploy_environment }}
    steps:
      - name: Determine environment from branch
        id: set-env
        run: |
          if [[ "${{ github.ref }}" == "refs/heads/master" ]]; then
            echo "environment=master" >> $GITHUB_OUTPUT
            echo "deploy_environment=production" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" == "refs/heads/staging" ]]; then
            echo "environment=staging" >> $GITHUB_OUTPUT
            echo "deploy_environment=staging" >> $GITHUB_OUTPUT
          else
            echo "environment=development" >> $GITHUB_OUTPUT
            echo "deploy_environment=development" >> $GITHUB_OUTPUT
          fi

  deploy-database:
    name: Deploy Database
    needs: setup
    runs-on: ubuntu-latest
    environment: ${{ needs.setup.outputs.environment }}
    env:
      SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: |
          npm install -g npm@latest
          npm ci

      - name: Install Supabase CLI
        uses: supabase/setup-cli@v1
        with:
          version: latest

      # Skip environment setup for master branch (production)
      - name: Setup environment
        if: needs.setup.outputs.environment != 'master'
        run: npm run env:ci:${{ needs.setup.outputs.environment }}

      # For master branch, use direct project ID
      - name: Link Supabase project (non-master)
        if: needs.setup.outputs.environment != 'master'
        run: |
          source .env.ci
          supabase link --project-ref $SUPABASE_PROJECT_ID

      # For master branch, use hardcoded project ID
      - name: Link Supabase project (master)
        if: needs.setup.outputs.environment == 'master'
        run: npm run supabase:link:master

      # For non-master branches, push all seed data
      - name: Push database changes (non-master)
        if: needs.setup.outputs.environment != 'master'
        run: npm run supabase:push

      # For master branch, only push schema without seed data
      - name: Push database schema (master)
        if: needs.setup.outputs.environment == 'master'
        run: supabase db push --db-only

      # Generate TypeScript types based on environment
      - name: Generate TypeScript types
        run: npm run generate-types:${{ needs.setup.outputs.environment }}

  build-and-deploy:
    name: Build and Deploy
    needs: [setup, deploy-database]
    runs-on: ubuntu-latest
    environment: ${{ needs.setup.outputs.environment }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: |
          npm install -g npm@latest
          npm ci

      # Skip environment setup for master branch (production)
      - name: Setup environment
        if: needs.setup.outputs.environment != 'master'
        run: npm run env:ci:${{ needs.setup.outputs.environment }}

      # For master branch, use hardcoded environment variables
      - name: Setup master environment
        if: needs.setup.outputs.environment == 'master'
        run: |
          echo "VITE_SUPABASE_URL=https://kkwjqitvnomdlgxtjvzy.supabase.co" > ./apps/web/.env
          echo "VITE_SUPABASE_ANON_KEY=${{ secrets.MASTER_SUPABASE_ANON_KEY }}" >> ./apps/web/.env
          echo "VITE_APP_ENV=production" >> ./apps/web/.env

      - name: Build
        run: npm run build

      # Deploy to Netlify
      - name: Deploy to Netlify
        uses: nwtgck/actions-netlify@v3.0
        with:
          publish-dir: './apps/web/dist'
          production-branch: master
          github-token: ${{ secrets.GITHUB_TOKEN }}
          deploy-message: "Deploy from GitHub Actions"
          enable-pull-request-comment: true
          enable-commit-comment: true
          overwrites-pull-request-comment: true
          alias: ${{ needs.setup.outputs.deploy_environment }}
        env:
          NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
          NETLIFY_SITE_ID: ${{ secrets.NETLIFY_SITE_ID }}
        timeout-minutes: 5
