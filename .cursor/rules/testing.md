---
# Specify the following for Cursor rules
description: Testing guidelines for Supabase Edge Functions
globs: "supabase/functions/tests/**/*.ts"
---

# Testing Supabase Edge Functions

This document outlines best practices for testing Supabase Edge Functions to ensure reliability and prevent common issues. It combines our own experience with official Supabase recommendations.

## Test Environment Setup

### Required Environment Variables

Create a `.env` file in the `supabase/functions` directory with the following variables:

```
# Supabase connection details
SUPABASE_URL=http://localhost:54321
SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

# Required for JWT validation
JWT_SECRET=super-secret-jwt-token-with-at-least-32-characters-long

# Test-specific variables
ENVIRONMENT=development
TESTING_MODE=true
SKIP_DB_VERIFICATION=true

# Service credentials (use testing credentials)
STRIPE_SECRET_KEY=sk_test_placeholder_for_testing
CAPTCHA_SECRET_KEY=captcha_test_placeholder
```

### Recommended Directory Structure

Following Supabase's recommendations:

```
supabase/functions/
├── function-one/           # Edge Function directory
│   └── index.ts            # Main function code
├── function-two/           # Another Edge Function
│   └── index.ts
├── tests/                  # All tests in one place
│   ├── helpers/            # Test utilities
│   │   ├── test-utils.ts   # Common test utilities
│   │   └── setup.ts        # Test setup
│   ├── unit/               # Unit tests
│   │   └── function-one-test.ts
│   ├── integration/        # Integration tests
│   │   └── function-two-test.ts
│   ├── mocks/              # Mock data
│   ├── run-tests.ts        # Test runner
│   └── .env                # Test environment variables
├── _shared/                # Shared code between functions
├── .env                    # Function environment variables
└── import_map.json         # Import map for dependencies
```

## Running Tests (CRITICAL - READ THIS SECTION)

### Correct Working Directory

⚠️ **IMPORTANT**: You MUST be in the `supabase/functions` directory when running tests!

```bash
# First, change to the supabase/functions directory
cd supabase/functions

# Then run the tests
deno run --allow-run --allow-env --allow-read tests/run-tests.ts --all
```

If you run from the project root, you'll get "not found" errors.

### Starting Supabase Services

Before running tests, ensure Supabase services are running:

```bash
# Start all Supabase services
supabase start

# Serve functions locally (in a separate terminal)
supabase functions serve
```

### Common Test Commands

```bash
# Run a specific test file
deno run --allow-run --allow-env --allow-read tests/run-tests.ts --file tests/integration/auth-failures-test.ts

# Run all tests
deno run --allow-run --allow-env --allow-read tests/run-tests.ts --all

# Generate coverage report
deno run --allow-run --allow-env --allow-read tests/run-tests.ts --all --coverage

# Direct Deno test command (alternative)
deno test --allow-all tests/integration/function-one-test.ts
```

### Fixed run-tests.ts file for Deno 2.x

For compatibility with Deno 2.x:

```typescript
// Replace Deno.run with Deno.Command
const command = new Deno.Command("deno", {
  args: ["test", ...baseArgs, ...testFiles],
  stdout: "inherit",
  stderr: "inherit",
});

const { code } = await command.output();
```

## Writing Effective Tests

### Test Structure Template

Based on Supabase's recommendations, here's a template for test files:

```typescript
// Import required libraries
import { assert, assertEquals } from "https://deno.land/std@0.224.0/assert/mod.ts";
import { createClient, SupabaseClient } from "jsr:@supabase/supabase-js@2";
// Load environment variables
import "https://deno.land/x/dotenv@v3.2.2/load.ts";

// Function to create a test client
function createTestClient(): SupabaseClient {
  const supabaseUrl = Deno.env.get("SUPABASE_URL") ?? "";
  const supabaseKey = Deno.env.get("SUPABASE_ANON_KEY") ?? "";

  if (!supabaseUrl) throw new Error("SUPABASE_URL environment variable is required");
  if (!supabaseKey) throw new Error("SUPABASE_ANON_KEY environment variable is required");

  return createClient(supabaseUrl, supabaseKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
      detectSessionInUrl: false,
    },
  });
}

// Test cases
Deno.test("Function should return expected result", async () => {
  const client = createTestClient();

  // Invoke your function
  const { data, error } = await client.functions.invoke("your-function-name", {
    body: { param1: "value1" },
  });

  // Consume the response body
  if (data) {
    console.log(JSON.stringify(data, null, 2));
  }

  // Assert results
  assert(!error, `Function returned an error: ${error?.message}`);
  assertEquals(data.someProperty, "expected value");
});
```

### Preventing Resource Leaks

Resource leaks are a common issue in Deno tests. Always consume response bodies:

```typescript
// When working with responses
const response = await client.functions.invoke("function-name", {
  method: "GET",
  // ... other options
});

// Consume the response body
if (response.data) {
  const _ = await Promise.resolve(response.data);
}

// When catching errors
try {
  // Your test code
} catch (error) {
  // Type cast error to access properties safely
  const typedError = error as ErrorResponse;

  // Consume the response body if it exists
  if (typedError.context && typedError.context.response) {
    try {
      await typedError.context.response.text();
    } catch (_) {
      // Ignore errors when consuming
    }
  }

  // Make assertions
}
```

### Type Safety in Tests

Define interfaces for error responses to ensure type safety:

```typescript
// Define type for error responses
interface ErrorResponse {
  status?: number;
  context?: {
    status?: number;
    response?: Response;
  };
  message?: string;
}
```

### Best Practices for Assertions

Use Deno's built-in assert functions:

```typescript
import { assert, assertEquals, assertExists } from "https://deno.land/std@0.224.0/assert/mod.ts";

// For boolean conditions
assert(condition, "Descriptive error message");

// For equality checks
assertEquals(actual, expected, "Optional message");

// For null/undefined checks
assertExists(value, "Value should exist");

// For status code assertions
assert(
  error.status === 401 || error.context?.status === 401 || error.message?.includes("401"),
  `Expected 401 status but got ${error.status || error.context?.status || "unknown"}`
);
```

### Testing with PostgreSQL Database Interaction

When testing functions that interact with the database:

```typescript
Deno.test("Database interaction test", async () => {
  const client = createTestClient();

  // First verify database connection
  const { data: tableData, error: tableError } = await client
    .from("your_table")
    .select("*")
    .limit(1);

  assert(!tableError, `Database error: ${tableError?.message}`);
  assertExists(tableData, "Database should return data");

  // Now test your function that interacts with the database
  const { data, error } = await client.functions.invoke("database-function", {
    body: { operation: "read" },
  });

  assert(!error, `Function error: ${error?.message}`);
  assertEquals(data.success, true);
});
```

### Mocking Dependencies

Use dependency injection for easier testing:

```typescript
// In your function
export function processPayment(stripeClient, data) {
  // Implementation using the client
}

// In your test
Deno.test("processPayment", async () => {
  const mockStripeClient = {
    charges: {
      create: (_params: any) => Promise.resolve({ id: "test_charge_id" }),
    },
  };

  const result = await processPayment(mockStripeClient, {
    amount: 1000,
    currency: "usd",
    source: "tok_visa",
  });

  assertEquals(result.success, true);
  assertEquals(result.chargeId, "test_charge_id");
});
```

## Common Testing Patterns

### Authentication Testing

Test both successful and failed authentication:

```typescript
// Test failed authentication
Deno.test("Function - Authentication Failure Test", async () => {
  const client = createInvalidClient(); // Client with invalid key

  try {
    await client.functions.invoke("function-name", {
      method: "GET",
      headers: { Authorization: `Bearer invalid_token` },
    });
    throw new Error("Expected authentication to fail but it succeeded");
  } catch (error) {
    const typedError = error as ErrorResponse;
    // Consume response body
    if (typedError.context?.response) {
      await typedError.context.response.text().catch(() => {});
    }
    // Assert unauthorized status
    assert(typedError.status === 401 || typedError.context?.status === 401);
  }
});

// Test successful authentication
Deno.test("Function - Authentication Success Test", async () => {
  const client = createValidClient(); // Client with valid key

  const { data, error } = await client.functions.invoke("function-name", {
    method: "GET",
    headers: { Authorization: `Bearer ${validToken}` },
  });

  // Consume the response body
  if (data) {
    const _ = await Promise.resolve(data);
  }

  assert(!error, `Function returned an error: ${error?.message}`);
  assertExists(data);
});
```

### Testing CORS Implementation

For functions that implement CORS:

```typescript
Deno.test("Function should handle OPTIONS request for CORS", async () => {
  const response = await fetch("http://localhost:54321/functions/v1/function-name", {
    method: "OPTIONS",
    headers: {
      Origin: "http://localhost:3000",
      "Access-Control-Request-Method": "POST",
      "Access-Control-Request-Headers": "Content-Type, Authorization",
    },
  });

  // Consume response body
  await response.text().catch(() => {});

  assertEquals(response.status, 200);
  assertEquals(response.headers.get("Access-Control-Allow-Origin"), "*");
  assertEquals(
    response.headers.get("Access-Control-Allow-Headers"),
    "authorization, x-client-info, apikey, content-type"
  );
});
```

### Testing External Service Integration

Use test flags to bypass actual external service calls:

```typescript
// In your function
if (Deno.env.get("TESTING_MODE") === "true") {
  console.log("Using mock external service in test mode");
  return mockResponse;
}
// Real implementation follows
```

### Database Test Setup and Cleanup

For tests that modify the database:

```typescript
Deno.test("Database modification test", async () => {
  const client = createTestClient();
  const testId = `test_${Date.now()}`;

  try {
    // Create test data
    const { error: insertError } = await client
      .from("your_table")
      .insert({ id: testId, name: "Test Item" });

    assert(!insertError, `Failed to insert test data: ${insertError?.message}`);

    // Test your function
    const { data, error } = await client.functions.invoke("your-function", {
      body: { id: testId },
    });

    // Assert results
    assert(!error, `Function returned error: ${error?.message}`);
    assertEquals(data.name, "Test Item");
  } finally {
    // Clean up test data
    await client.from("your_table").delete().eq("id", testId);
  }
});
```

## Using Test Hooks

Take advantage of Deno's test hooks for setup and teardown:

```typescript
// Setup - runs before each test
const setup = () => {
  console.log("Setting up test");
  // Setup code here
  return "some-context";
};

// Teardown - runs after each test
const teardown = (context: string) => {
  console.log(`Tearing down test with context: ${context}`);
  // Cleanup code here
};

Deno.test({
  name: "Test with hooks",
  fn: async () => {
    // Test code here
    assert(true);
  },
  sanitizeResources: false, // Allows for controlled resource cleanup
  sanitizeOps: false, // Allows for controlled op cleanup
  setup,
  teardown,
});
```

## Troubleshooting Common Issues

### Path Issues

If you see "not found" errors:

1. Make sure you're in the `supabase/functions` directory when running tests
2. Make sure all paths in test commands are relative to that directory
3. Don't add `supabase/functions/` to test file paths when already in that directory

### JWT Verification Errors

If you see JWT verification errors:

1. Make sure JWT_SECRET is set in your .env file
2. Check that the JWT_SECRET matches what Supabase is using
3. Verify your function uses the correct environment variable

### Resource Leaks

If you see "Leaks detected" errors:

1. Make sure all response bodies are consumed
2. Make sure all streams are properly closed
3. Make sure background tasks are properly handled

### Deno Version Compatibility

If you see errors about Deno.run:

1. Update run-tests.ts to use Deno.Command instead (see example above)
2. Make sure you're using Deno assertions compatible with your Deno version

### Database Connection Issues

If tests involving the database fail:

1. Verify Supabase is running locally with `supabase status`
2. Check that your SUPABASE_URL and SUPABASE_ANON_KEY are correct
3. Make sure the database tables exist and are accessible
4. Check for PostgreSQL-specific errors in the output

### Local Function Service Issues

If function invocation fails:

1. Make sure edge functions are being served with `supabase functions serve`
2. Check the terminal running the functions serve command for errors
3. Verify the function name in your test matches the actual function name
4. Try invoking the function manually using curl to isolate the issue

## Performance Testing Considerations

When testing performance:

1. Use Deno's built-in performance API:

   ```typescript
   const start = performance.now();
   // Code to test
   const end = performance.now();
   console.log(`Operation took ${end - start}ms`);
   ```

2. Implement response time assertions:

   ```typescript
   Deno.test("Function performance test", async () => {
     const start = performance.now();
     const { data, error } = await client.functions.invoke("your-function");
     const duration = performance.now() - start;

     assert(!error);
     assert(duration < 200, `Function took too long: ${duration}ms`);
   });
   ```

## Advanced Testing Techniques

### Parallel Testing

Run tests in parallel for faster execution:

```typescript
// Will run in parallel
Deno.test("Test 1", async () => {
  /* ... */
});
Deno.test("Test 2", async () => {
  /* ... */
});

// Force sequential testing when needed
Deno.test({
  name: "Sequential test 1",
  fn: async () => {
    /* ... */
  },
  sequential: true,
});
Deno.test({
  name: "Sequential test 2",
  fn: async () => {
    /* ... */
  },
  sequential: true,
});
```

### Snapshot Testing

For API responses that should remain consistent:

```typescript
Deno.test("API response matches snapshot", async () => {
  const client = createTestClient();

  const { data, error } = await client.functions.invoke("api-function");
  assert(!error);

  // Compare with expected response
  const expectedResponse = {
    status: "success",
    message: "Operation completed",
    data: [
      /* expected data structure */
    ],
  };

  assertEquals(data, expectedResponse);
});
```

### Fuzz Testing

Test with random inputs to discover edge cases:

```typescript
Deno.test("Function handles random inputs", async () => {
  const client = createTestClient();

  // Generate random test cases
  for (let i = 0; i < 10; i++) {
    const randomInput = generateRandomInput(); // Your random generation function

    const { data, error } = await client.functions.invoke("your-function", {
      body: { input: randomInput },
    });

    // Should either succeed or fail gracefully, but not crash
    if (error) {
      assert(error.message, "Error should have a message");
    } else {
      assertExists(data);
    }
  }
});
```

## Command Line Testing Tools

### Curl Commands

Test your endpoints directly from the command line:

```bash
# Basic GET request
curl -i http://localhost:54321/functions/v1/function-name

# POST request with data
curl -i -X POST \
  -H "Content-Type: application/json" \
  -d '{"param1": "value1"}' \
  http://localhost:54321/functions/v1/function-name

# Request with authentication
curl -i -X GET \
  -H "Authorization: Bearer your_jwt_token" \
  http://localhost:54321/functions/v1/function-name
```

### Local Network Testing

Test from different devices on your network:

```bash
# First, find your local IP
# For Linux/macOS
ifconfig | grep "inet "
# For Windows
ipconfig

# Then use that IP instead of localhost
curl -i http://YOUR_LOCAL_IP:54321/functions/v1/function-name
```

## Containerized Testing

### Docker-Based Testing

Create a `Dockerfile.test` for consistent testing across environments:

```dockerfile
FROM denoland/deno:alpine

WORKDIR /app

# Install Supabase CLI
RUN apk add --no-cache curl unzip \
    && curl -fsSL https://github.com/supabase/cli/releases/download/v1.36.6/supabase_1.36.6_linux_amd64.tar.gz | tar -xz \
    && mv supabase /usr/local/bin

# Copy function code and tests
COPY . .

# Set up environment
ENV SUPABASE_URL=http://host.docker.internal:54321
ENV SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
ENV TESTING_MODE=true

# Run tests
CMD ["run", "--allow-all", "tests/run-tests.ts", "--all"]
```

Run with:

```bash
docker build -f Dockerfile.test -t supabase-function-tests .
docker run --network=host supabase-function-tests
```

## Comprehensive Testing Checklist

Before considering your Edge Function fully tested, verify these items:

- [ ] All environment variables are properly set up and read
- [ ] Authentication and authorization logic is thoroughly tested
- [ ] Error handling is tested for all possible error conditions
- [ ] Resource leaks are eliminated by consuming all response bodies
- [ ] CORS headers are properly set and tested for web client access
- [ ] Performance is measured and meets requirements
- [ ] Tests run successfully on Windows, macOS, and Linux
- [ ] Test coverage meets or exceeds 80% threshold
- [ ] Edge cases and boundary conditions are tested
- [ ] Database interactions are tested with proper setup and cleanup
- [ ] External service dependencies are mocked appropriately

## Testing Complex Edge Functions

### Testing Streaming Responses

For functions that return streaming responses:

```typescript
Deno.test("Function returns streamed data", async () => {
  // Direct fetch for streaming response
  const response = await fetch("http://localhost:54321/functions/v1/stream-function");

  assert(response.ok, "Response should be successful");
  assert(response.body, "Response should have a body");

  // Read the stream
  const reader = response.body!.getReader();
  let chunks = [];
  let done = false;

  while (!done) {
    const { value, done: doneReading } = await reader.read();
    done = doneReading;
    if (value) {
      chunks.push(value);
    }
  }

  // Process the data
  const allData = new Uint8Array(chunks.reduce((acc, chunk) => acc + chunk.length, 0));
  let position = 0;

  for (const chunk of chunks) {
    allData.set(chunk, position);
    position += chunk.length;
  }

  const decodedData = new TextDecoder().decode(allData);
  assert(decodedData.includes("expected content"), "Stream should contain expected content");
});
```

### Testing WebSocket Connections

For functions that use WebSockets:

```typescript
Deno.test("WebSocket communication test", async () => {
  // Create WebSocket connection
  const ws = new WebSocket("ws://localhost:54321/functions/v1/websocket-function");

  // Set up message tracking
  const messages: string[] = [];
  let connectionClosed = false;

  ws.onmessage = (event) => {
    messages.push(event.data);
  };

  ws.onclose = () => {
    connectionClosed = true;
  };

  // Wait for connection
  await new Promise<void>((resolve) => {
    ws.onopen = () => resolve();
  });

  // Send test message
  ws.send(JSON.stringify({ type: "test", data: "hello" }));

  // Wait for response (with timeout)
  await new Promise<void>((resolve) => {
    const checkMessages = () => {
      if (messages.length > 0 || connectionClosed) {
        resolve();
      } else {
        setTimeout(checkMessages, 100);
      }
    };
    setTimeout(checkMessages, 100);
    // Timeout after 5 seconds
    setTimeout(resolve, 5000);
  });

  // Clean up
  ws.close();

  // Assert
  assert(messages.length > 0, "Should receive at least one message");
  assert(messages[0].includes("expected response"), "Message should contain expected response");
});
```

### Testing File Uploads and Downloads

For functions that handle file operations:

```typescript
Deno.test("File upload and download test", async () => {
  const client = createTestClient();

  // Create test file
  const testData = new Uint8Array([1, 2, 3, 4, 5]);
  const formData = new FormData();
  formData.append("file", new Blob([testData]), "test.bin");

  // Upload file
  const { data: uploadData, error: uploadError } = await client.functions.invoke("file-handler", {
    method: "POST",
    body: formData,
  });

  assert(!uploadError, `Upload failed: ${uploadError?.message}`);
  assert(uploadData.fileId, "Upload should return a file ID");

  // Download the file
  const { data: downloadData, error: downloadError } = await client.functions.invoke(
    "file-handler",
    {
      method: "GET",
      queryParams: { fileId: uploadData.fileId },
    }
  );

  assert(!downloadError, `Download failed: ${downloadError?.message}`);

  // Compare file content
  const downloadedArray = new Uint8Array(downloadData.buffer);
  assertEquals(downloadedArray.length, testData.length, "File size should match");

  for (let i = 0; i < testData.length; i++) {
    assertEquals(downloadedArray[i], testData[i], `Byte at position ${i} should match`);
  }
});
```

## Real-World Testing Scenarios

### Load Testing

Test function performance under load:

```typescript
Deno.test("Function handles concurrent requests", async () => {
  const client = createTestClient();
  const concurrentRequests = 10;
  const startTime = performance.now();

  // Run multiple requests in parallel
  const promises = Array.from({ length: concurrentRequests }, () =>
    client.functions.invoke("your-function", {
      body: { param: "value" },
    })
  );

  const results = await Promise.all(promises);
  const endTime = performance.now();

  // Check all responses were successful
  for (let i = 0; i < results.length; i++) {
    const { error } = results[i];
    assert(!error, `Request ${i} failed: ${error?.message}`);
  }

  // Check performance
  const totalTime = endTime - startTime;
  const avgTime = totalTime / concurrentRequests;
  console.log(
    `Average request time: ${avgTime}ms under load of ${concurrentRequests} concurrent requests`
  );

  // Should handle load reasonably
  assert(avgTime < 500, `Average response time (${avgTime}ms) too slow under load`);
});
```

### End-to-End Testing

Test the full user flow through multiple functions:

```typescript
Deno.test("End-to-end user signup and profile flow", async () => {
  const client = createTestClient();
  const testEmail = `test${Date.now()}@example.com`;
  const testPassword = "TestPassword123!";

  // Step 1: Create user account
  const { data: signupData, error: signupError } = await client.functions.invoke("auth-signup", {
    body: {
      email: testEmail,
      password: testPassword,
      name: "Test User",
    },
  });

  assert(!signupError, `Signup failed: ${signupError?.message}`);
  assert(signupData.userId, "Signup should return a user ID");

  // Step 2: Login
  const { data: loginData, error: loginError } = await client.functions.invoke("auth-login", {
    body: {
      email: testEmail,
      password: testPassword,
    },
  });

  assert(!loginError, `Login failed: ${loginError?.message}`);
  assert(loginData.token, "Login should return a token");

  // Step 3: Get user profile
  const { data: profileData, error: profileError } = await client.functions.invoke("user-profile", {
    headers: {
      Authorization: `Bearer ${loginData.token}`,
    },
  });

  assert(!profileError, `Profile fetch failed: ${profileError?.message}`);
  assertEquals(profileData.email, testEmail, "Profile should contain correct email");
  assertEquals(profileData.name, "Test User", "Profile should contain correct name");

  // Step 4: Clean up test data
  await client.functions.invoke("delete-test-user", {
    body: { userId: signupData.userId },
    headers: {
      "X-Admin-Key": Deno.env.get("ADMIN_TEST_KEY") || "",
    },
  });
});
```

## Windows-Specific Testing Guidelines

When running tests on Windows, there are some specific considerations to keep in mind:

### Path Formatting

Windows uses backslashes (`\`) in file paths, but Deno generally works better with forward slashes (`/`):

```bash
# Preferred format (works on all platforms)
deno test --allow-net --allow-env --allow-read --allow-write --env-file=tests/.env tests/integration/verify-captcha-test.ts

# Windows-specific format (may be necessary in some cases)
deno test --allow-net --allow-env --allow-read --allow-write --env-file=tests\.env "tests\integration\verify-captcha-test.ts"
```

### PowerShell Considerations

PowerShell has different escaping rules than bash. If you're using PowerShell:

```powershell
# PowerShell example
deno test --allow-net --allow-env --allow-read --allow-write --env-file=tests/.env tests/integration/verify-captcha-test.ts
```

### Dealing with Resource Leaks on Windows

The most common resource leak error on Windows is unconsumed fetch response bodies:

```
error: Leaks detected:
  - A fetch response body was created during the test, but not consumed during the test. Consume or close the response body `ReadableStream`, e.g `await resp.text()` or `await resp.body.cancel()`.
```

To fix this, always consume response bodies, even for error cases:

```typescript
// When testing error responses
try {
  const response = await client.functions.invoke("function-name", {
    // ...
  });
  // Process successful response
} catch (error) {
  // For error objects that might contain a response
  if (error && typeof error === "object" && "context" in error) {
    const typedError = error as ErrorResponse;
    if (typedError.context?.response) {
      try {
        // Consume the response body to prevent leaks
        await typedError.context.response.text();
      } catch (_) {
        // Ignore errors in error handling
      }
    }
  }
  // Continue with error assertions
}
```

## No Test Modules Found Error

If you encounter a "No test modules found" error:

1. **Check file naming convention**: Make sure your test files end with `-test.ts` or `.test.ts`

2. **Verify file content**: Ensure your test files contain `Deno.test()` function calls

3. **Run tests directly**: Try running a specific test file directly with the full path:

   ```powershell
   deno test --allow-net --allow-env --allow-read --allow-write --env-file=tests/.env "C:\path\to\project\supabase\functions\tests\integration\verify-captcha-test.ts"
   ```

4. **List test files first**: Check what test files actually exist:
   ```powershell
   Get-ChildItem -Path tests\integration\*-test.ts -Name
   ```

## Cross-Platform Test Execution

For running tests across different operating systems (Ubuntu, Windows, macOS):

1. **Use relative paths with forward slashes**: Forward slashes work on all platforms

   ```typescript
   // Instead of this (Windows-specific)
   const path = "tests\\integration\\file.ts";

   // Use this (works everywhere)
   const path = "tests/integration/file.ts";
   ```

2. **Use Deno's path module for platform-compatible path handling**:
   ```typescript
   import { join } from "https://deno.land/std@0.224.0/path/mod.ts";
   const testPath = join("tests", "integration", "file.ts");
   ```

## Conclusion

Following these comprehensive testing guidelines will ensure your Supabase Edge Functions are reliable, performant, and work consistently across all platforms. Remember that thorough testing not only catches bugs before they reach production but also serves as documentation for how the functions should behave.

For any questions or additional help with testing, consult the Supabase documentation or reach out to the community for support.
